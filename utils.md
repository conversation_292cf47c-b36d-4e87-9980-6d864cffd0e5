```markdown
<!-- docs/utils_reference.md -->
# Utilities Developer Reference  
*(for `src/eeg_quality_assessment/utils/`)*

This file documents the **internal utility layer** that all quality modules rely on.  
Everything here is **imported by name** and unit-tested independently.

---

## 1. Directory Layout

```
utils/
├── __init__.py            # Re-exports public utilities
├── signal_utils.py        # Pure signal-processing helpers
├── file_io.py             # Multi-format EEG I/O
├── validation.py          # Defensive checks & sanitizers
└── metrics.py             # Re-usable quality metrics
```

---

## 2. signal_utils.py

> Low-level **NumPy/SciPy** only – no MNE objects.

| Function | Signature | Purpose |
|---|---|---|
| `bandpower` | `arr: np.ndarray, fs: int, band: Tuple[float,float], win_sec: float=4, relative: bool=False` → `float` | Welch-based band power (dB or ratio) |
| `clip_ratio` | `arr: np.ndarray, max_uv: float` → `float` | % samples clipped |
| `slew_rate` | `arr: np.ndarray, fs: int` → `np.ndarray` | First derivative (µV/s) |
| `window_rms` | `arr: np.ndarray, win_len: int` → `np.ndarray` | Rolling RMS |
| `detrend_poly` | `arr: np.ndarray, order: int=2` → `np.ndarray` | Polynomial baseline removal |
| `santitize_nan` | `arr: np.ndarray, method: str="zero"` → `np.ndarray` | Handles NaNs |

Usage example:
```python
from eeg_quality_assessment.utils.signal_utils import bandpower
alpha_power = bandpower(data, fs=1000, band=(8, 13))
```

---

## 3. file_io.py

> Thin adapters around **MNE-Python**, **PyEDFlib**, etc.  
> Returns **raw NumPy + metadata dict** so modules stay format-agnostic.

| Function | Signature | Notes |
|---|---|---|
| `load_signal` | `path: Union[str,Path]` → `Tuple[np.ndarray, dict]` | Auto-detects `.edf .bdf .fif .set` |
| `save_numpy` | `path: Path, arr: np.ndarray, meta: dict` | `.npz` archive |
| `supported_extensions` | `()` → `List[str]` | `['.edf', '.bdf', ...]` |
| `detect_format` | `path: Path` → `str` | Returns lowercase ext |

Metadata dict keys:
```python
{
    "sfreq": 1000,
    "ch_names": ["Fp1", "Fp2", ...],
    "duration_sec": 600.0,
    "n_ch": 64,
    "file_type": "edf"
}
```

---

## 4. validation.py

> **Fail-fast** guards used at the top of every public entry point.

| Function | Raises | Description |
|---|---|---|
| `check_file_exists` | `FileNotFoundError` | w/ helpful message |
| `check_channels` | `ValueError` | Ensures ≥1 channel |
| `check_duration` | `ValueError` | Rejects too-short recordings |
| `check_sample_rate` | `ValueError` | Warns < 250 Hz |
| `sanitize_channel_names` | `List[str]` | Removes spaces & special chars |
| `validate_config` | `jsonschema.ValidationError` | Validates user YAML against schema |

Example:
```python
from eeg_quality_assessment.utils.validation import validate_config
validate_config(user_dict)   # raises if invalid
```

---

## 5. metrics.py

> **Vectorised** implementations reused across modules.

| Function | Signature | Returns |
|---|---|---|
| `hurst_exponent` | `arr: np.ndarray` → `float` | 0-1 (higher = smoother) |
| `sample_entropy` | `arr: np.ndarray, m: int=2, r: float=0.2*std` → `float` | |
| `line_noise_power` | `arr: np.ndarray, fs: int, freq: int=50` → `float` | dB |
| `hjorth_parameters` | `arr: np.ndarray` → `Tuple[float,float]` | (mobility, complexity) |
| `zero_crossing_rate` | `arr: np.ndarray` → `float` | |

All functions:
- Accept **any 1-D array** (channel or already-epoched data).
- Return **scalar** unless otherwise noted.
- Are **Numba-accelerated** where beneficial (decorated with `@njit`).

---

## 6. Re-export in `__init__.py`

To keep imports short:

```python
from .signal_utils import bandpower, clip_ratio
from .file_io import load_signal, supported_extensions
from .validation import check_file_exists, validate_config
from .metrics import sample_entropy, line_noise_power

__all__ = [
    "bandpower", "clip_ratio",
    "load_signal", "supported_extensions",
    "check_file_exists", "validate_config",
    "sample_entropy", "line_noise_power",
]
```

External usage:
```python
from eeg_quality_assessment.utils import load_signal, bandpower
```

---

## 7. Testing Contract

Every utility has **dedicated pytest** in `tests/test_utils/`.

File naming:
- `test_signal_utils.py`
- `test_file_io.py`
- `test_validation.py`
- `test_metrics.py`

Golden rule:  
> *“If a helper grows >40 lines, split or refactor.”*

---

## 8. Performance & Memory Notes

- **No copies** unless explicitly stated (e.g., `detrend_poly`).
- All NumPy arrays are **C-contiguous float32** internally.
- Chunking for >1 GB files handled *outside* these utils (in `EEGProcessor`).

---

## 9. Future Extension Points

| Need | Plan |
|---|---|
| HDF5 export | Add `save_hdf5()` in `file_io.py` |
| GPU metrics | Create `metrics_gpu.py`, dispatch via `cupy` |
| Stream processing | Add ring-buffer helpers in `signal_utils.py` |

---
```