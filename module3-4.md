# EEG Quality Assessment - Modules 3 & 4 Implementation

## Module 3: Spectral Quality Analyzer
**File**: `src/modules/spectral_analysis.py`

### Purpose
Analyze frequency domain characteristics to assess signal quality, neural content, and spectral artifacts.

### Implementation Requirements

#### Class Structure
```python
from .base_module import BaseQualityModule
import numpy as np
from scipy import signal, stats
from fooof import FOOOF
import matplotlib.pyplot as plt

class SpectralQualityAnalyzer(BaseQualityModule):
    def __init__(self, config: Dict = None):
        super().__init__("Spectral Quality Analyzer", config)
        self.thresholds = {
            'alpha_peak_range': (8, 13),
            'min_alpha_power': 0.1,
            'max_line_noise_ratio': 0.05,
            'min_1f_r_squared': 0.8,
            'spectral_entropy_range': (0.3, 0.9)
        }
```

#### Core Spectral Methods

1. **Power Spectral Density Quality Assessment**
```python
def analyze_psd_quality(self, signal: np.ndarray, fs: float, 
                       method: str = 'welch') -> Dict:
    """
    Assess PSD smoothness, structure, and quality indicators
    """
    # Calculate PSD using Welch's method
    # Assess spectral smoothness (derivative analysis)
    # Check for spectral structure vs. flat spectrum
    # Identify spectral peaks and notches
    # Calculate spectral quality metrics
    
    freqs, psd = signal.welch(signal, fs, nperseg=2*fs)
    
    quality_metrics = {
        'spectral_smoothness': self._calculate_spectral_smoothness(psd),
        'spectral_structure_score': self._assess_spectral_structure(freqs, psd),
        'frequency_resolution': freqs[1] - freqs[0],
        'dynamic_range_db': 10 * np.log10(np.max(psd) / np.min(psd))
    }
    
    return quality_metrics
```

2. **1/f Slope Analysis (FOOOF)**
```python
def analyze_1f_slope(self, signal: np.ndarray, fs: float, 
                    freq_range: Tuple[float, float] = (1, 40)) -> Dict:
    """
    Fit 1/f background and assess neural vs artifactual content
    """
    # Calculate PSD
    # Initialize FOOOF model
    # Fit 1/f background and peaks
    # Extract slope, offset, and R-squared
    # Assess quality of fit
    
    freqs, psd = signal.welch(signal, fs, nperseg=2*fs)
    
    # Initialize FOOOF object
    fm = FOOOF(peak_width_limits=[1, 8], max_n_peaks=6)
    
    # Fit model
    fm.fit(freqs, psd, freq_range)
    
    return {
        'slope': fm.aperiodic_params_[1],
        'offset': fm.aperiodic_params_[0],
        'r_squared': fm.r_squared_,
        'n_peaks': len(fm.peak_params_),
        'peak_frequencies': fm.peak_params_[:, 0] if len(fm.peak_params_) > 0 else [],
        'fit_quality': 'good' if fm.r_squared_ > 0.8 else 'poor'
    }
```

3. **Spectral Entropy Analysis**
```python
def calculate_spectral_entropy(self, signal: np.ndarray, fs: float, 
                              freq_bands: Dict = None) -> Dict:
    """
    Calculate spectral entropy as measure of frequency complexity
    """
    if freq_bands is None:
        freq_bands = {
            'delta': (0.5, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30),
            'gamma': (30, 100)
        }
    
    freqs, psd = signal.welch(signal, fs, nperseg=2*fs)
    
    # Normalize PSD to create probability distribution
    psd_norm = psd / np.sum(psd)
    
    # Calculate Shannon entropy
    spectral_entropy = -np.sum(psd_norm * np.log2(psd_norm + 1e-15))
    
    # Calculate band-specific entropy
    band_entropy = {}
    for band_name, (low_freq, high_freq) in freq_bands.items():
        band_mask = (freqs >= low_freq) & (freqs <= high_freq)
        if np.any(band_mask):
            band_psd = psd_norm[band_mask]
            band_entropy[band_name] = -np.sum(band_psd * np.log2(band_psd + 1e-15))
    
    return {
        'total_spectral_entropy': spectral_entropy,
        'normalized_entropy': spectral_entropy / np.log2(len(psd_norm)),
        'band_entropy': band_entropy,
        'entropy_quality': 'optimal' if 0.3 <= spectral_entropy/np.log2(len(psd_norm)) <= 0.9 else 'suboptimal'
    }
```

4. **Alpha Peak Detection and Quality**
```python
def detect_alpha_peak(self, signal: np.ndarray, fs: float) -> Dict:
    """
    Detect and characterize alpha rhythm quality
    """
    freqs, psd = signal.welch(signal, fs, nperseg=2*fs)
    
    # Focus on alpha range
    alpha_mask = (freqs >= 8) & (freqs <= 13)
    alpha_freqs = freqs[alpha_mask]
    alpha_psd = psd[alpha_mask]
    
    if len(alpha_psd) == 0:
        return {'alpha_detected': False, 'quality': 'no_alpha'}
    
    # Find peak in alpha range
    peak_idx = np.argmax(alpha_psd)
    peak_freq = alpha_freqs[peak_idx]
    peak_power = alpha_psd[peak_idx]
    
    # Calculate alpha characteristics
    total_power = np.sum(psd)
    alpha_power_ratio = np.sum(alpha_psd) / total_power
    
    # Peak prominence
    prominence = peak_power / np.median(alpha_psd)
    
    # Peak width analysis
    peak_width = self._calculate_peak_width(alpha_freqs, alpha_psd, peak_idx)
    
    return {
        'alpha_detected': True,
        'peak_frequency': peak_freq,
        'peak_power': peak_power,
        'alpha_power_ratio': alpha_power_ratio,
        'prominence': prominence,
        'peak_width': peak_width,
        'quality': self._assess_alpha_quality(prominence, peak_width)
    }
```

5. **Line Noise Analysis**
```python
def analyze_line_noise(self, signal: np.ndarray, fs: float, 
                      line_freq: float = 50) -> Dict:
    """
    Detailed analysis of power line interference
    """
    freqs, psd = signal.welch(signal, fs, nperseg=4*fs)
    
    # Define line noise frequencies (fundamental + harmonics)
    line_freqs = [line_freq * i for i in range(1, 6) if line_freq * i < fs/2]
    
    line_noise_metrics = {}
    total_line_power = 0
    
    for harm_freq in line_freqs:
        # Find power at harmonic frequency
        freq_idx = np.argmin(np.abs(freqs - harm_freq))
        
        # Define narrow window around line frequency
        window_width = 2  # Hz
        freq_mask = (freqs >= harm_freq - window_width) & (freqs <= harm_freq + window_width)
        
        line_power = np.sum(psd[freq_mask])
        total_line_power += line_power
        
        # Calculate SNR at this harmonic
        noise_floor = np.median(psd[freq_mask])
        snr = psd[freq_idx] / noise_floor
        
        line_noise_metrics[f'{harm_freq}Hz'] = {
            'power': line_power,
            'snr': snr,
            'frequency_exact': freqs[freq_idx]
        }
    
    # Overall line noise assessment
    total_power = np.sum(psd)
    line_noise_ratio = total_line_power / total_power
    
    return {
        'line_noise_ratio': line_noise_ratio,
        'harmonics_detected': len(line_noise_metrics),
        'harmonic_details': line_noise_metrics,
        'contamination_level': self._classify_line_noise(line_noise_ratio)
    }

#### Helper Methods for Module 3
```python
def _calculate_spectral_smoothness(self, psd: np.ndarray) -> float:
    """Calculate spectral smoothness using derivative analysis"""
    log_psd = np.log10(psd + 1e-15)
    derivatives = np.diff(log_psd)
    smoothness = 1.0 / (1.0 + np.std(derivatives))
    return smoothness

def _assess_spectral_structure(self, freqs: np.ndarray, psd: np.ndarray) -> float:
    """Assess presence of spectral structure vs flat spectrum"""
    # Look for peaks and structure in the spectrum
    from scipy.signal import find_peaks
    peaks, _ = find_peaks(psd, prominence=np.std(psd))
    structure_score = min(len(peaks) / 10.0, 1.0)  # Normalize to 0-1
    return structure_score

def _calculate_peak_width(self, freqs: np.ndarray, psd: np.ndarray, peak_idx: int) -> float:
    """Calculate width of spectral peak at half maximum"""
    peak_power = psd[peak_idx]
    half_max = peak_power / 2.0
    
    # Find left and right boundaries
    left_idx = peak_idx
    while left_idx > 0 and psd[left_idx] > half_max:
        left_idx -= 1
    
    right_idx = peak_idx
    while right_idx < len(psd) - 1 and psd[right_idx] > half_max:
        right_idx += 1
    
    return freqs[right_idx] - freqs[left_idx]

def _assess_alpha_quality(self, prominence: float, peak_width: float) -> str:
    """Assess alpha peak quality based on prominence and width"""
    if prominence > 3.0 and 1.0 <= peak_width <= 4.0:
        return 'excellent'
    elif prominence > 2.0 and 0.5 <= peak_width <= 6.0:
        return 'good'
    elif prominence > 1.5:
        return 'fair'
    else:
        return 'poor'

def _classify_line_noise(self, line_noise_ratio: float) -> str:
    """Classify line noise contamination level"""
    if line_noise_ratio < 0.01:
        return 'minimal'
    elif line_noise_ratio < 0.05:
        return 'moderate'
    else:
        return 'severe'
```

#### Quality Score and Flags for Module 3
```python
def calculate_quality_score(self, metrics: Dict) -> float:
    """
    Combine spectral metrics into 0-100 quality score
    Weight factors:
    - 1/f slope fit quality: 25%
    - Alpha peak presence/quality: 20%
    - Line noise level: 20%
    - Spectral entropy: 15%
    - PSD structure: 10%
    - Spectral smoothness: 10%
    """
    score = 0.0
    
    # 1/f slope quality (25%)
    if '1f_analysis' in metrics:
        r_squared = metrics['1f_analysis']['r_squared']
        score += 25 * min(r_squared / 0.9, 1.0)
    
    # Alpha peak quality (20%)
    if 'alpha_analysis' in metrics and metrics['alpha_analysis']['alpha_detected']:
        alpha_quality = metrics['alpha_analysis']['quality']
        alpha_scores = {'excellent': 1.0, 'good': 0.8, 'fair': 0.6, 'poor': 0.3}
        score += 20 * alpha_scores.get(alpha_quality, 0.0)
    
    # Line noise (20% - inverted, less noise = higher score)
    if 'line_noise' in metrics:
        noise_ratio = metrics['line_noise']['line_noise_ratio']
        score += 20 * max(0, 1.0 - noise_ratio / 0.1)
    
    # Spectral entropy (15%)
    if 'spectral_entropy' in metrics:
        norm_entropy = metrics['spectral_entropy']['normalized_entropy']
        # Optimal range is 0.3-0.9
        if 0.3 <= norm_entropy <= 0.9:
            score += 15
        else:
            score += 15 * (1.0 - abs(norm_entropy - 0.6) / 0.6)
    
    # PSD structure (10%)
    if 'psd_quality' in metrics:
        structure_score = metrics['psd_quality']['spectral_structure_score']
        score += 10 * structure_score
    
    # Spectral smoothness (10%)
    if 'psd_quality' in metrics:
        smoothness = metrics['psd_quality']['spectral_smoothness']
        score += 10 * smoothness
    
    return min(score, 100.0)
```

#### Flag Generation for Module 3
- `LINE_NOISE`: Significant power line interference detected
- `POOR_SPECTRUM`: Poor spectral structure or quality
- `NO_ALPHA`: No detectable alpha rhythm
- `FLAT_SPECTRUM`: Lack of spectral structure
- `POOR_1F_FIT`: Poor fit to 1/f background model

---

## Module 4: Temporal Consistency Checker
**File**: `src/modules/temporal_consistency.py`

### Purpose
Assess signal stability, stationarity, and temporal consistency to identify non-physiological variations and recording instabilities.

### Implementation Requirements

#### Class Structure
```python
from .base_module import BaseQualityModule
import numpy as np
from scipy import stats
from arch.unitroot import ADF
from statsmodels.tsa.stattools import adfuller

class TemporalConsistencyChecker(BaseQualityModule):
    def __init__(self, config: Dict = None):
        super().__init__("Temporal Consistency Checker", config)
        self.thresholds = {
            'adf_p_value': 0.05,
            'min_autocorr_lag1': 0.3,
            'max_variance_cv': 0.5,
            'min_segment_correlation': 0.5
        }
```

#### Core Temporal Analysis Methods

1. **Autocorrelation Function Analysis**
```python
def analyze_autocorrelation(self, signal: np.ndarray, fs: float, 
                           max_lag_seconds: float = 2.0) -> Dict:
    """
    Analyze signal autocorrelation for self-similarity assessment
    """
    max_lag_samples = int(max_lag_seconds * fs)
    
    # Calculate autocorrelation function
    autocorr = np.correlate(signal, signal, mode='full')
    autocorr = autocorr[len(autocorr)//2:]  # Take positive lags only
    autocorr = autocorr / autocorr[0]  # Normalize
    
    # Limit to specified max lag
    autocorr = autocorr[:max_lag_samples]
    lags = np.arange(len(autocorr)) / fs
    
    # Key autocorrelation metrics
    lag1_autocorr = autocorr[1] if len(autocorr) > 1 else 0
    
    # Find first zero crossing
    zero_crossing_idx = np.where(autocorr <= 0)[0]
    first_zero_lag = lags[zero_crossing_idx[0]] if len(zero_crossing_idx) > 0 else max_lag_seconds
    
    # Exponential decay fit
    try:
        # Fit exponential decay: autocorr = exp(-t/tau)
        valid_indices = autocorr > 0
        if np.sum(valid_indices) > 10:
            log_autocorr = np.log(autocorr[valid_indices])
            fit_lags = lags[valid_indices]
            slope, intercept, r_value, p_value, std_err = stats.linregress(fit_lags, log_autocorr)
            decay_constant = -1.0 / slope if slope < 0 else np.inf
            fit_quality = r_value**2
        else:
            decay_constant = np.nan
            fit_quality = 0.0
    except:
        decay_constant = np.nan
        fit_quality = 0.0
    
    return {
        'lag1_autocorr': lag1_autocorr,
        'first_zero_crossing': first_zero_lag,
        'decay_constant': decay_constant,
        'exponential_fit_r2': fit_quality,
        'autocorr_function': autocorr[:min(100, len(autocorr))],  # Store first 100 points
        'quality_assessment': self._assess_autocorr_quality(lag1_autocorr, decay_constant)
    }

def _assess_autocorr_quality(self, lag1_autocorr: float, decay_constant: float) -> str:
    """Assess autocorrelation quality"""
    if lag1_autocorr > 0.7 and 0.1 < decay_constant < 2.0:
        return 'excellent'
    elif lag1_autocorr > 0.5 and 0.05 < decay_constant < 5.0:
        return 'good'
    elif lag1_autocorr > 0.3:
        return 'fair'
    else:
        return 'poor'
```

2. **Stationarity Testing**
```python
def test_stationarity(self, signal: np.ndarray, fs: float, 
                     test_type: str = 'adf') -> Dict:
    """
    Perform stationarity tests using Augmented Dickey-Fuller test
    """
    # Augmented Dickey-Fuller test
    try:
        adf_result = adfuller(signal, autolag='AIC')
        adf_statistic = adf_result[0]
        adf_pvalue = adf_result[1]
        adf_critical_values = adf_result[4]
        
        is_stationary = adf_pvalue < 0.05
        
    except Exception as e:
        adf_statistic = np.nan
        adf_pvalue = 1.0
        adf_critical_values = {}
        is_stationary = False
    
    # Additional stationarity indicators
    # Rolling statistics analysis
    window_size = min(len(signal) // 10, int(fs * 10))  # 10-second windows or 1/10 signal length
    
    rolling_mean = self._calculate_rolling_statistic(signal, window_size, np.mean)
    rolling_std = self._calculate_rolling_statistic(signal, window_size, np.std)
    
    # Assess stability of rolling statistics
    mean_stability = 1.0 / (1.0 + np.std(rolling_mean) / (np.abs(np.mean(rolling_mean)) + 1e-6))
    std_stability = 1.0 / (1.0 + np.std(rolling_std) / (np.mean(rolling_std) + 1e-6))
    
    return {
        'adf_statistic': adf_statistic,
        'adf_pvalue': adf_pvalue,
        'adf_critical_values': adf_critical_values,
        'is_stationary': is_stationary,
        'mean_stability': mean_stability,
        'std_stability': std_stability,
        'rolling_mean': rolling_mean,
        'rolling_std': rolling_std,
        'stationarity_confidence': self._assess_stationarity_confidence(adf_pvalue, mean_stability, std_stability)
    }

def _calculate_rolling_statistic(self, signal: np.ndarray, window_size: int, stat_func) -> np.ndarray:
    """Calculate rolling statistics"""
    n_windows = len(signal) // window_size
    if n_windows < 2:
        return np.array([stat_func(signal)])
    
    rolling_stats = []
    for i in range(n_windows):
        start_idx = i * window_size
        end_idx = (i + 1) * window_size
        window_stat = stat_func(signal[start_idx:end_idx])
        rolling_stats.append(window_stat)
    
    return np.array(rolling_stats)

def _assess_stationarity_confidence(self, adf_pvalue: float, mean_stab: float, std_stab: float) -> str:
    """Assess confidence in stationarity assessment"""
    if adf_pvalue < 0.01 and mean_stab > 0.8 and std_stab > 0.8:
        return 'high'
    elif adf_pvalue < 0.05 and mean_stab > 0.6 and std_stab > 0.6:
        return 'moderate'
    else:
        return 'low'
```

3. **Running Variance Analysis**
```python
def analyze_running_variance(self, signal: np.ndarray, fs: float, 
                           window_seconds: float = 2.0) -> Dict:
    """
    Analyze variance changes over time
    """
    window_samples = int(window_seconds * fs)
    step_size = window_samples // 2  # 50% overlap
    
    variances = []
    timestamps = []
    
    for start in range(0, len(signal) - window_samples, step_size):
        window = signal[start:start + window_samples]
        variance = np.var(window)
        variances.append(variance)
        timestamps.append(start / fs)
    
    variances = np.array(variances)
    timestamps = np.array(timestamps)
    
    # Variance statistics
    mean_variance = np.mean(variances)
    variance_cv = np.std(variances) / (mean_variance + 1e-15)  # Coefficient of variation
    
    # Detect variance outliers
    variance_z_scores = np.abs(stats.zscore(variances))
    outlier_threshold = 3.0
    n_outliers = np.sum(variance_z_scores > outlier_threshold)
    outlier_percentage = n_outliers / len(variances) * 100
    
    # Trend in variance
    if len(variances) > 2:
        slope, intercept, r_value, p_value, std_err = stats.linregress(timestamps, variances)
        variance_trend_significance = p_value < 0.05
    else:
        slope = 0.0
        variance_trend_significance = False
    
    return {
        'mean_variance': mean_variance,
        'variance_cv': variance_cv,
        'variance_outliers': n_outliers,
        'outlier_percentage': outlier_percentage,
        'variance_trend_slope': slope,
        'trend_significant': variance_trend_significance,
        'variance_timeseries': variances,
        'timestamps': timestamps,
        'stability_assessment': self._assess_variance_stability(variance_cv, outlier_percentage)
    }

def _assess_variance_stability(self, cv: float, outlier_pct: float) -> str:
    """Assess variance stability"""
    if cv < 0.2 and outlier_pct < 5:
        return 'excellent'
    elif cv < 0.4 and outlier_pct < 10:
        return 'good'
    elif cv < 0.6 and outlier_pct < 20:
        return 'fair'
    else:
        return 'poor'
```

4. **Trend Detection**
```python
def detect_trends(self, signal: np.ndarray, fs: float) -> Dict:
    """
    Detect linear and polynomial trends in the signal
    """
    time_vector = np.arange(len(signal)) / fs
    
    # Linear trend analysis
    linear_slope, linear_intercept, linear_r, linear_p, linear_stderr = stats.linregress(time_vector, signal)
    linear_trend_significant = linear_p < 0.05
    
    # Detrended signal for further analysis
    linear_trend = linear_slope * time_vector + linear_intercept
    detrended_linear = signal - linear_trend
    
    # Quadratic trend analysis
    try:
        quad_coeffs = np.polyfit(time_vector, signal, 2)
        quad_trend = np.polyval(quad_coeffs, time_vector)
        detrended_quad = signal - quad_trend
        
        # Compare quadratic vs linear fit
        quad_residual_var = np.var(detrended_quad)
        linear_residual_var = np.var(detrended_linear)
        quad_improvement = (linear_residual_var - quad_residual_var) / linear_residual_var
        
    except:
        quad_coeffs = [0, 0, np.mean(signal)]
        quad_improvement = 0.0
    
    # Assess trend severity
    signal_range = np.ptp(signal)  # Peak-to-peak
    trend_range = np.ptp(linear_trend)
    trend_contribution = trend_range / signal_range if signal_range > 0 else 0
    
    return {
        'linear_slope': linear_slope,
        'linear_r_squared': linear_r**2,
        'linear_p_value': linear_p,
        'linear_significant': linear_trend_significant,
        'quadratic_coeffs': quad_coeffs,
        'quadratic_improvement': quad_improvement,
        'trend_contribution_ratio': trend_contribution,
        'detrended_signal': detrended_linear,
        'trend_severity': self._assess_trend_severity(trend_contribution, linear_p)
    }

def _assess_trend_severity(self, trend_ratio: float, p_value: float) -> str:
    """Assess severity of detected trends"""
    if p_value > 0.05:
        return 'no_trend'
    elif trend_ratio < 0.1:
        return 'minimal'
    elif trend_ratio < 0.3:
        return 'moderate'
    else:
        return 'severe'
```

5. **Segment-to-Segment Correlation**
```python
def analyze_segment_correlation(self, signal: np.ndarray, fs: float, 
                              segment_seconds: float = 5.0) -> Dict:
    """
    Analyze correlation between consecutive signal segments
    """
    segment_samples = int(segment_seconds * fs)
    n_segments = len(signal) // segment_samples
    
    if n_segments < 2:
        return {
            'n_segments': n_segments,
            'correlation_analysis': 'insufficient_data',
            'mean_correlation': np.nan,
            'min_correlation': np.nan,
            'consistency_score': 0.0
        }
    
    # Extract segments
    segments = []
    for i in range(n_segments):
        start_idx = i * segment_samples
        end_idx = (i + 1) * segment_samples
        segment = signal[start_idx:end_idx]
        segments.append(segment)
    
    # Calculate all pairwise correlations
    correlations = []
    for i in range(len(segments)):
        for j in range(i + 1, len(segments)):
            try:
                corr, _ = stats.pearsonr(segments[i], segments[j])
                if not np.isnan(corr):
                    correlations.append(corr)
            except:
                continue
    
    correlations = np.array(correlations)
    
    if len(correlations) == 0:
        mean_corr = np.nan
        min_corr = np.nan
        consistency_score = 0.0
    else:
        mean_corr = np.mean(correlations)
        min_corr = np.min(correlations)
        consistency_score = np.mean(correlations > 0.3)  # Fraction of "good" correlations
    
    # Adjacent segment correlations (most important)
    adjacent_correlations = []
    for i in range(len(segments) - 1):
        try:
            corr, _ = stats.pearsonr(segments[i], segments[i + 1])
            if not np.isnan(corr):
                adjacent_correlations.append(corr)
        except:
            continue
    
    adjacent_correlations = np.array(adjacent_correlations)
    mean_adjacent_corr = np.mean(adjacent_correlations) if len(adjacent_correlations) > 0 else np.nan
    
    return {
        'n_segments': n_segments,
        'all_correlations': correlations,
        'mean_correlation': mean_corr,
        'min_correlation': min_corr,
        'adjacent_correlations': adjacent_correlations,
        'mean_adjacent_correlation': mean_adjacent_corr,
        'consistency_score': consistency_score,
        'consistency_assessment': self._assess_segment_consistency(mean_adjacent_corr, consistency_score)
    }

def _assess_segment_consistency(self, adj_corr: float, consistency_score: float) -> str:
    """Assess segment-to-segment consistency"""
    if np.isnan(adj_corr):
        return 'insufficient_data'
    elif adj_corr > 0.7 and consistency_score > 0.8:
        return 'excellent'
    elif adj_corr > 0.5 and consistency_score > 0.6:
        return 'good'
    elif adj_corr > 0.3 and consistency_score > 0.4:
        return 'fair'
    else:
        return 'poor'
```

#### Quality Score and Flags for Module 4
```python
def calculate_quality_score(self, metrics: Dict) -> float:
    """
    Combine temporal consistency metrics into 0-100 quality score
    Weight factors:
    - Stationarity: 30%
    - Autocorrelation quality: 25%
    - Variance stability: 20%
    - Segment consistency: 15%
    - Trend severity: 10%
    """
    score = 0.0
    
    # Stationarity (30%)
    if 'stationarity' in metrics:
        if metrics['stationarity']['is_stationary']:
            confidence = metrics['stationarity']['stationarity_confidence']
            conf_scores = {'high': 1.0, 'moderate': 0.7, 'low': 0.4}
            score += 30 * conf_scores.get(confidence, 0.0)
    
    # Autocorrelation (25%)
    if 'autocorr' in metrics:
        quality = metrics['autocorr']['quality_assessment']
        quality_scores = {'excellent': 1.0, 'good': 0.8, 'fair': 0.6, 'poor': 0.3}
        score += 25 * quality_scores.get(quality, 0.0)
    
    # Variance stability (20%)
    if 'variance' in metrics:
        stability = metrics['variance']['stability_assessment']
        stability_scores = {'excellent': 1.0, 'good': 0.8, 'fair': 0.6, 'poor': 0.3}
        score += 20 * stability_scores.get(stability, 0.0)
    
    # Segment consistency (15%)
    if 'segments' in metrics:
        consistency = metrics['segments']['consistency_assessment']
        consistency_scores = {'excellent': 1.0, 'good': 0.8, 'fair': 0.6, 'poor': 0.3}
        score += 15 * consistency_scores.get(consistency, 0.0)
    
    # Trend severity (10% - inverted, less trend = higher score)
    if 'trends' in metrics:
        severity = metrics['trends']['trend_severity']
        severity_scores = {'no_trend': 1.0, 'minimal': 0.9, 'moderate': 0.6, 'severe': 0.2}
        score += 10 * severity_scores.get(severity, 0.0)
    
    return min(score, 100.0)
```

#### Flag Generation for Module 4
- `NON_STATIONARY`: Signal fails stationarity tests
- `TRENDING`: Significant linear or polynomial trends detected
- `INCONSISTENT`: Poor correlation between signal segments
- `UNSTABLE`: High variance in temporal characteristics
- `POOR_AUTOCORR`: Poor autocorrelation structure

### Implementation Notes

#### Computational Considerations
- Use efficient algorithms for rolling statistics
- Consider memory usage for very long signals
- Implement parallel processing for segment analysis

#### Edge Cases
- Handle very short signals (< 10 seconds)
- Manage signals with extreme artifacts
- Deal with constant or near-constant signals

#### Configuration
```yaml
# In config/thresholds.yaml
temporal_consistency:
  stationarity:
    adf_significance: 0.05
    min_data_length: 1000  # samples
  autocorrelation:
    max_lag_seconds: 2.0
    min_lag1_correlation: 0.3
  variance:
    analysis_window_seconds: 2.0
    max_cv_threshold: 0.5
  segments:
    segment_duration_seconds: 5.0
    min_correlation_threshold: 0.5
```
```