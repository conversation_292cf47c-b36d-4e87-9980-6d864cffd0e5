# Setup and Configuration Guide  
*(final version – ready to use)*

This document explains how to **install, configure and customise** the **EEG Quality Assessment Tool**.  
It replaces and unifies the previous `setting_config.md` and any fragments scattered in `start.md`.

---

## 1. Quick-Start Installation

### 1.1 From source *(recommended for now)*
```bash
git clone https://github.com/yourusername/eeg-quality-assessment.git
cd eeg-quality-assessment
python -m venv venv
source venv/bin/activate   # Windows: venv\Scripts\activate
pip install -e .
```

### 1.2 From PyPI *(when published)*
```bash
pip install eeg-quality-assessment
```

Verify the install:
```bash
eeg-quality --help
```

---

## 2. File Layout at a Glance

```
eeg-quality-assessment/
├── setup.py
├── pyproject.toml
├── src/
│   └── eeg_quality_assessment/     # Python package
├── config/
│   ├── settings.py                 # Dynamic settings
│   └── thresholds.yaml             # Scientific thresholds
├── examples/
├── docs/
└── tests/
```

---

## 3. Configuration Overview

All runtime behaviour is governed by **three layers** (last one wins):

| Layer            | File / Source                                   | Editable | Purpose |
|------------------|--------------------------------------------------|----------|---------|
| **Defaults**     | `config/settings.py` + `config/thresholds.yaml` | No       | Safe baseline |
| **User file**    | Any YAML file you point to (`--config`)         | Yes      | Per-project tuning |
| **CLI flags**    | Command-line options                            | Yes      | One-off overrides |

---

## 4. The `thresholds.yaml` Reference

*(Scientific constants – change only if you know why)*

```yaml
# EEG Quality Assessment Thresholds Configuration
# Copy this file, edit, and pass with `eeg-quality run --config my_thresholds.yaml`

signal_integrity:
  min_amplitude_uv: 1.0
  max_amplitude_uv: 200.0
  max_saturation_ratio: 0.01
  min_snr_db: 10.0
  max_baseline_drift: 5.0          # µV / min
  amplitude_consistency_threshold: 0.8

artifact_detection:
  muscle_artifact:
    threshold_uv: 100.0
    frequency_range: [20, 200]
    min_duration_ms: 100
  eye_artifact:
    threshold_uv: 150.0
    channels: ['Fp1','Fp2','F7','F8']
    min_duration_ms: 200
  line_noise:
    max_power_db: -20.0
    frequencies: [50, 60]
    harmonics: [2, 3, 4]
  movement_artifact:
    threshold_uv: 200.0
    frequency_range: [0.5, 10]

spectral_analysis:
  frequency_bands:
    delta: [0.5, 4]
    theta: [4, 8]
    alpha: [8, 13]
    beta: [13, 30]
    gamma: [30, 100]
  power_thresholds:
    min_total_power_db: -60
    max_alpha_ratio: 0.6
    min_spectral_entropy: 0.3
  spectral_quality:
    smoothness_threshold: 0.8
    peak_consistency: 0.7

temporal_consistency:
  stationarity:
    window_length_s: 10.0
    overlap_ratio: 0.5
    min_stationarity_score: 0.7
  drift_analysis:
    max_drift_uv_per_min: 5.0
    polynomial_order: 2
  discontinuity:
    max_discontinuity_ratio: 0.05
    threshold_factor: 5.0

complexity_entropy:
  entropy_measures:
    sample_entropy_r: 0.2
    approximate_entropy_r: 0.2
  complexity_measures:
    lz_complexity_threshold: 0.3
    fractal_dimension_range: [1.2, 1.8]
  temporal_features:
    hjorth_mobility_range: [0.1, 2.0]
    hjorth_complexity_range: [0.5, 3.0]

channel_correlation:
  correlation_thresholds:
    min_correlation: 0.3
    max_correlation: 0.95
    bad_channel_threshold: 0.2
  spatial_analysis:
    neighbor_distance_mm: 50
    reference_channels: ['Cz','FCz','CPz']
  network_analysis:
    clustering_threshold: 0.6
    connectivity_threshold: 0.5

impedance_contact:
  impedance_thresholds:
    max_impedance_kohm: 10.0
    impedance_balance_threshold: 2.0
  contact_quality:
    voltage_offset_threshold_uv: 50.0
    signal_stability_threshold: 0.8
  electrode_specific:
    reference_impedance_kohm: 5.0
    ground_impedance_kohm: 2.0

overall_quality:
  score_ranges:
    excellent: [0.9, 1.0]
    good: [0.7, 0.9]
    fair: [0.5, 0.7]
    poor: [0.3, 0.5]
    unacceptable: [0.0, 0.3]
  module_weights:
    signal_integrity: 0.25
    artifact_detection: 0.20
    spectral_analysis: 0.15
    temporal_consistency: 0.15
    complexity_entropy: 0.10
    channel_correlation: 0.10
    impedance_contact: 0.05
  confidence_factors:
    min_duration_s: 60
    min_channels: 8
    sample_rate_factor: 1.0

analysis_parameters:
  preprocessing:
    highpass_freq: 0.5
    lowpass_freq: 100.0
    notch_freq: [50, 60]
  segmentation:
    segment_length_s: 30.0
    overlap_ratio: 0.5
    min_segment_length_s: 10.0
  parallel_processing:
    max_workers: null            # null = auto
    chunk_size: 1000
```

---

## 5. Dynamic Settings (`settings.py`)

The file `config/settings.py` is **code-driven configuration** – useful when embedding the package in larger pipelines.  
Users normally **do not edit this file**; instead, override via YAML or CLI flags.

Key dataclasses you may import:

```python
from eeg_quality_assessment.config.settings import AnalysisConfig, ModuleConfig
```

---

## 6. User Configuration File Example

Create `my_project_config.yaml`:

```yaml
analysis:
  analysis_depth: "comprehensive"   # fast | standard | comprehensive
  selected_modules:                 # leave empty for all
    - signal_integrity
    - artifact_detection
    - spectral_analysis
  preprocessing:
    highpass_freq: 1.0
    lowpass_freq: 80.0
    notch_freq: [50]
    apply_car: true
    apply_ica: false
  parallel_processing:
    enabled: true
    max_workers: 8
    chunk_size: 2000

output:
  format: "both"            # pdf | json | both
  include_plots: true
  plot_settings:
    figure_size: [14, 9]
    dpi: 300
    style: "seaborn"
    color_palette: "plasma"
  report_sections:
    executive_summary: true
    detailed_results: true
    recommendations: true
    technical_details: false

logging:
  level: "DEBUG"
  file_logging: true
  file_path: "./logs/eeg_quality.log"
```

Run with:

```bash
eeg-quality run signal.edf --config my_project_config.yaml
```

---

## 7. CLI Flags Quick Reference

| Flag | Example | Over-rides |
|------|---------|------------|
| `--config` | `--config my.yaml` | Entire file |
| `--modules` | `--modules artifact,spectral` | `selected_modules` |
| `--parallel` | `--parallel 8` | `max_workers` |
| `--format` | `--format pdf` | `output.format` |
| `--quick` | `--quick` | `analysis_depth = fast` |
| `--verbose` | `--verbose` | `logging.level = DEBUG` |

---

## 8. Environment Variables *(optional)*

| Variable | Description | Default |
|----------|-------------|---------|
| `EEG_QA_CONFIG_DIR` | Directory where personal configs live | `~/.config/eeg_quality` |
| `EEG_QA_CACHE_DIR` | Cache for large intermediate files | `~/.cache/eeg_quality` |

---

## 9. Common Customisation Scenarios

### 9.1 I only want to run two modules
```bash
eeg-quality run signal.edf --modules signal_integrity,artifact_detection
```

### 9.2 I need stricter saturation limits
Copy `thresholds.yaml`, change `max_saturation_ratio: 0.005`, then:
```bash
eeg-quality run signal.edf --config my_strict.yaml
```

### 9.3 I embed the package in my own script
```python
from eeg_quality_assessment import EEGProcessor

proc = EEGProcessor(
    signal_path="data/file.edf",
    config_path="my_config.yaml"   # optional
)
results = proc.run_analysis()
print(results["overall_quality"]["score"])
```

---

## 10. Troubleshooting

| Symptom | Cause / Fix |
|---------|-------------|
| `ModuleNotFoundError: No module named 'xyz'` | `pip install -e .` again |
| `Configuration file not found` | Provide absolute path or place file next to data |
| `Out of memory` | Reduce `chunk_size`, set `max_workers: 2` |
| `Plots not showing` | Ensure `matplotlib` backend: `export MPLBACKEND=Agg` |

---

## 11. Next Steps

- See [User Guide](../docs/user_guide.md) for step-by-step tutorials.  
- See [Module Specifications](../docs/module_specifications.md) to understand each metric.  
- Run the examples in `examples/` for copy-paste snippets.

---

**End of Configuration Guide**