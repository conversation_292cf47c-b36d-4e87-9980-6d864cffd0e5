```markdown
<!-- docs/ai_agent_instructions.md -->
# AI Agent Instructions – Generating Documentation, Examples & Output Skeleton

> **Role:** You are the **AI Documentation & Example Generator** for the EEG Quality Assessment Tool.  
> **Goal:** Create **production-ready** user docs, API docs, runnable examples, and a sane output folder layout **exactly** as listed below.

---

## 1. Directory Tree to Generate

```
docs/
├── api_documentation.md          # Public Python API
├── user_guide.md                 # Step-by-step user walkthrough
├── module_specifications.md      # Scientific & algorithmic details
└── technical_specs.md            # Internal architecture & dev notes

examples/
├── basic_usage.py                # Minimal CLI + Python API demo
├── batch_processing.py           # Directory → batch reports
├── custom_module.py              # Skeleton + instructions to author new module
└── advanced_config.py            # Deep-dive on YAML overrides

output/
├── reports/                      # *empty* – PDF & JSON land here
├── plots/                        # *empty* – PNG/SVG/Plotly HTML
└── data/                         # *empty* – serialized .npz/.csv intermediates
```

---

## 2. Content Requirements

### 2.1 `docs/api_documentation.md`
- Markdown, auto-doc style (Google-style docstrings).
- **Sections:**
  1. Package entry points (`eeg_quality_assessment.EEGProcessor`)
  2. CLI (`eeg-quality` commands & flags)
  3. Configuration dataclasses (`AnalysisConfig`, `ModuleConfig`)
  4. Base class (`BaseQualityModule`) – abstract methods
  5. Utility helpers (`load_signal`, `aggregate_scores`)
- Include **type hints** and **minimal code snippets** (3–5 lines).

### 2.2 `docs/user_guide.md`
- Target audience: **non-dev researchers**.
- **Sections:**
  1. Installation (pip/conda/docker)
  2. 30-second quick-start (single file → PDF)
  3. Understanding the generated PDF report (screenshots as ASCII art placeholders)
  4. Custom thresholds walkthrough (edit YAML)
  5. Batch processing multiple files
  6. FAQ / Troubleshooting
- Use **numbered steps**, **copy-paste commands**, **screenshots placeholders** (`![plot](./output/plots/placeholder.png)`).

### 2.3 `docs/module_specifications.md`
- **Per-module** scientific detail:
  - Purpose in one sentence
  - Input assumptions (sampling rate, units)
  - Algorithms (cite papers in footnotes)
  - Output metrics table (name, range, interpretation)
  - Configurable thresholds (link to YAML keys)
- Diagrams: *ASCII flowcharts only*.

### 2.4 `docs/technical_specs.md`
- Audience: **future maintainers**.
- **Sections:**
  1. High-level architecture diagram (ASCII)
  2. Data flow (signal → modules → aggregator → report)
  3. Extension points (how to add new module)
  4. Parallelism strategy (`concurrent.futures`)
  5. Memory & performance notes (streaming vs. in-mem)
  6. Testing strategy (unit / integration / perf)

### 2.5 `examples/*.py`
- **Shebang + docstring** at top.
- **Self-contained**: runnable with only `python file.py` (download sample data if missing).
- **Progress bars** (`tqdm`) for long loops.
- **Print final paths**:  
  `print("Report saved to:", report_path)`.

#### 2.5.1 `basic_usage.py`
```python
#!/usr/bin/env python3
"""
basic_usage.py
Demonstrate single-file quality assessment.
"""
from pathlib import Path
from eeg_quality_assessment import EEGProcessor

SIGNAL = Path("tests/sample_data/test_signal.edf")
proc = EEGProcessor(SIGNAL, config_path=None)
result = proc.run_analysis()
proc.generate_report("output/reports/basic_report", format="both")
print("Done → output/reports/basic_report.pdf")
```

#### 2.5.2 `batch_processing.py`
- Iterate over `input_dir/*.edf`.
- Save reports in `output/reports/{stem}.pdf`.
- Use 4 workers, tqdm progress bar.

#### 2.5.3 `custom_module.py`
- Skeleton inheriting `BaseQualityModule`.
- Placeholder metric: ratio of high-amplitude samples.
- Register in YAML snippet + run.

#### 2.5.4 `advanced_config.py`
- Override *all* major options: depth, thresholds, parallel, plots.
- Use temporary YAML created inline via `tempfile`.

### 2.6 `output/*`
- **Do NOT commit real data**.
- Add `.gitkeep` files so git tracks empty dirs:
```
output/reports/.gitkeep
output/plots/.gitkeep
output/data/.gitkeep
```

---

## 3. Markdown Style Guide

- **Filenames**: lower_snake.md
- **Headings**: `##` top-level, `###` sub-levels.
- **Code blocks**: triple back-ticks with language tag (` ```python `).
- **Links**: relative paths only (`../examples/basic_usage.py`).
- **No HTML tags** (portability).

---

## 4. Final Checklist

| Task | Status |
|------|--------|
| All four docs generated | ☐ |
| All four examples runnable (`python file.py`) | ☐ |
| `.gitkeep` in every empty output subdir | ☐ |
| All internal links valid (no 404) | ☐ |
| Spell-check pass (`cspell`) | ☐ |

---

## 5. Output Token Limit

- **Total markdown lines** across all docs ≤ 1,200.
- **Each example** ≤ 80 lines (including comments).
- **ASCII diagrams only** (no images).

---
```