# EEG Quality Assessment - Requirements and Dependencies

## Python Version
- **Minimum**: Python 3.8
- **Recommended**: Python 3.9+

## Core Dependencies

### Signal Processing & Analysis
```
numpy>=1.21.0              # Numerical computations
scipy>=1.7.0               # Scientific computing and signal processing
mne>=1.0.0                 # EEG/MEG data processing
pyedflib>=0.1.30          # EDF/BDF file reading
matplotlib>=3.5.0          # Basic plotting
seaborn>=0.11.0           # Statistical visualizations
```

### Advanced Analysis
```
scikit-learn>=1.0.0        # Machine learning metrics and tools
pandas>=1.3.0              # Data manipulation and analysis
antropy>=0.1.4             # Entropy and complexity measures
fooof>=1.0.0               # Spectral parameterization
pywavelets>=1.1.1          # Wavelet analysis
spectrum>=0.8.0            # Advanced spectral analysis
```

### Statistical Analysis
```
statsmodels>=0.13.0        # Statistical tests and models
pingouin>=0.5.0           # Statistical functions
arch>=5.0.0               # Time series analysis (stationarity tests)
```

### File I/O and Data Handling
```
h5py>=3.1.0               # HDF5 file support
pymatreader>=0.0.30       # MATLAB file reading
openpyxl>=3.0.0           # Excel file support
pyyaml>=6.0               # YAML configuration files
```

### Visualization and Reporting
```
plotly>=5.0.0             # Interactive plots
bokeh>=2.4.0              # Web-based plotting
reportlab>=3.6.0          # PDF generation
jinja2>=3.0.0             # Template engine for reports
weasyprint>=54.0          # HTML to PDF conversion
```

### Parallel Processing and Performance
```
joblib>=1.1.0             # Parallel computing
numba>=0.56.0             # JIT compilation for speed
tqdm>=4.62.0              # Progress bars
psutil>=5.8.0             # System monitoring
```

### Development and Testing
```
pytest>=6.2.0            # Testing framework
pytest-cov>=3.0.0        # Coverage testing
black>=21.0.0             # Code formatting
flake8>=4.0.0             # Code linting
mypy>=0.910               # Type checking
sphinx>=4.0.0             # Documentation generation
```

## Requirements.txt File
Create `requirements.txt` with the following content:

```txt
# Core dependencies
numpy>=1.21.0
scipy>=1.7.0
mne>=1.0.0
pyedflib>=0.1.30
matplotlib>=3.5.0
seaborn>=0.11.0

# Advanced analysis
scikit-learn>=1.0.0
pandas>=1.3.0
antropy>=0.1.4
fooof>=1.0.0
pywavelets>=1.1.1
spectrum>=0.8.0

# Statistical analysis
statsmodels>=0.13.0
pingouin>=0.5.0
arch>=5.0.0

# File I/O
h5py>=3.1.0
pymatreader>=0.0.30
openpyxl>=3.0.0
pyyaml>=6.0

# Visualization and reporting
plotly>=5.0.0
bokeh>=2.4.0
reportlab>=3.6.0
jinja2>=3.0.0
weasyprint>=54.0

# Performance
joblib>=1.1.0
numba>=0.56.0
tqdm>=4.62.0
psutil>=5.8.0
```

## Development Requirements (requirements-dev.txt)
```txt
pytest>=6.2.0
pytest-cov>=3.0.0
black>=21.0.0
flake8>=4.0.0
mypy>=0.910
sphinx>=4.0.0
jupyter>=1.0.0
ipywidgets>=7.6.0
```

## Optional Dependencies

### GPU Acceleration (Optional)
```
cupy>=9.0.0               # GPU-accelerated computing
```

### Advanced Signal Processing (Optional)
```
nilearn>=0.8.0            # Neuroimaging analysis
neurodsp>=2.0.0           # Neural signal processing
yasa>=0.5.0               # Sleep analysis tools
```

## Installation Instructions

### Standard Installation
```bash
# Create virtual environment
python -m venv eeg_quality_env
source eeg_quality_env/bin/activate  # Linux/Mac
# or eeg_quality_env\Scripts\activate  # Windows

# Install dependencies
pip install -r requirements.txt
```

### Development Installation
```bash
# Install with development dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Install in development mode
pip install -e .
```

### System-Specific Notes

#### Linux/Ubuntu
```bash
# May need system packages for some dependencies
sudo apt-get install python3-dev
sudo apt-get install libblas-dev liblapack-dev
sudo apt-get install libffi-dev
```

#### macOS
```bash
# Install with conda for better compatibility
conda install -c conda-forge mne
pip install -r requirements.txt
```

#### Windows
```bash
# Consider using conda for scientific packages
conda install numpy scipy matplotlib
pip install -r requirements.txt
```

## Docker Support (Future)
Create `Dockerfile` for containerized deployment:
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "src/main.py"]
```

## Dependency Management Notes

### Version Pinning Strategy
- Pin major and minor versions for stability
- Allow patch updates for security fixes
- Regular dependency audits and updates

### Performance Considerations
- NumPy and SciPy are critical for performance
- Consider Intel Math Kernel Library (MKL) for optimized NumPy
- Numba for JIT compilation of compute-intensive functions

### Memory Management
- Large EEG files may require memory-mapped arrays
- Implement chunked processing for very long recordings
- Monitor memory usage with psutil

## Testing Requirements
All dependencies must be testable in CI/CD environment:
- Unit tests for each module
- Integration tests for full pipeline
- Performance benchmarks
- Memory usage testing