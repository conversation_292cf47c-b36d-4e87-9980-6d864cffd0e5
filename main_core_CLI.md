# EEG Quality Assessment - Core System Architecture

## Overview
This document specifies the core system components: main orchestrator, results aggregator, configuration management, signal loading, and utility functions that tie all quality assessment modules together according to the updated architecture.

---

## CORE SYSTEM ARCHITECTURE

### Updated File Structure
```
src/
├── __init__.py
├── main.py                    # CLI entry point
├── core/
│   ├── __init__.py
│   ├── base_module.py         # Abstract base class for modules
│   ├── eeg_processor.py       # Main orchestrator
│   ├── aggregator.py          # Results aggregation
│   ├── config_manager.py      # Configuration handling
│   └── signal_loader.py       # Multi-format signal loading
├── modules/
│   ├── __init__.py
│   ├── signal_integrity.py    # Module 1: Basic signal validation
│   ├── artifact_detection.py  # Module 2: Artifact identification
│   ├── spectral_analysis.py   # Module 3: Frequency domain analysis
│   ├── temporal_consistency.py# Module 4: Time-based quality metrics
│   ├── complexity_entropy.py  # Module 5: Complexity measures
│   ├── channel_correlation.py # Module 6: Cross-channel analysis
│   └── impedance_contact.py   # Module 7: Contact quality assessment
├── utils/
│   ├── __init__.py
│   ├── signal_utils.py        # Signal processing utilities
│   ├── file_io.py            # File I/O handlers
│   ├── validation.py         # Input validation
│   └── metrics.py            # Quality metrics calculations
└── visualization/
    ├── __init__.py
    ├── signal_plots.py        # Time series visualization
    ├── spectral_plots.py      # Frequency plots
    ├── quality_dashboard.py   # Overall quality view
    ├── report_generator.py    # PDF report creation
    └── plot_utils.py          # Common plotting functions
```

---

## 1. ABSTRACT BASE MODULE CLASS

### File: `src/core/base_module.py`

### Purpose
Define the standardized interface that all quality assessment modules must implement, ensuring consistency and modularity.

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import time
import logging

class BaseQualityModule(ABC):
    """Abstract base class for all EEG quality assessment modules"""
    
    def __init__(self, name: str, sampling_rate: float, config: dict = None):
        self.name = name
        self.sampling_rate = sampling_rate
        self.config = config or {}
        
        # Results storage
        self.results = {}
        self.quality_score = None
        self.execution_time = 0.0
        self.flags = []
        self.recommendations = []
        self.visualizations = []
        
        # Setup logging
        self.logger = logging.getLogger(f"EEG.{self.name}")
        
    @abstractmethod
    def analyze(self, signal: np.ndarray, metadata: dict = None) -> dict:
        """
        Main analysis method - must be implemented by each module
        
        Parameters:
        - signal: EEG signal data (channels x samples for multichannel)
        - metadata: Additional signal information (sampling_rate, channel_names, etc.)
        
        Returns:
        - results: Dictionary containing all computed metrics
        """
        pass
    
    @abstractmethod
    def get_score(self) -> float:
        """
        Calculate and return normalized quality score (0-100)
        
        Returns:
        - score: Quality score between 0 (poor) and 100 (excellent)
        """
        pass
    
    @abstractmethod
    def get_recommendations(self) -> List[str]:
        """
        Generate actionable quality improvement suggestions
        
        Returns:
        - recommendations: List of specific improvement suggestions
        """
        pass
    
    def get_flags(self) -> List[dict]:
        """
        Return quality warning flags with timestamps and severity
        
        Returns:
        - flags: List of flag dictionaries with keys: 'type', 'severity', 'message', 'timestamp'
        """
        return self.flags
    
    def get_visualizations(self) -> List[dict]:
        """
        Return plot specifications for report generation
        
        Returns:
        - plots: List of plot specifications for visualization module
        """
        return self.visualizations
    
    def get_results(self) -> Dict[str, Any]:
        """Return standardized results dictionary"""
        return {
            "module_name": self.name,
            "quality_score": self.get_score(),
            "flags": self.get_flags(),
            "recommendations": self.get_recommendations(),
            "metrics": self.results,
            "execution_time": self.execution_time,
            "config_used": self.config,
            "visualizations": self.get_visualizations()
        }
    
    def _time_execution(self, func):
        """Decorator to time module execution"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            self.execution_time = time.time() - start_time
            return result
        return wrapper
    
    def validate_signal(self, signal: np.ndarray) -> bool:
        """
        Comprehensive signal validation before analysis
        
        Parameters:
        - signal: Input EEG signal
        
        Returns:
        - valid: True if signal passes validation
        
        Raises:
        - ValueError: If signal fails validation checks
        """
        if not isinstance(signal, np.ndarray):
            raise ValueError(f"{self.name}: Signal must be numpy array")
        
        if signal.size == 0:
            raise ValueError(f"{self.name}: Empty signal provided")
            
        if np.any(np.isnan(signal)) or np.any(np.isinf(signal)):
            raise ValueError(f"{self.name}: Signal contains NaN or infinite values")
        
        # Check signal dimensions
        if signal.ndim > 2:
            raise ValueError(f"{self.name}: Signal must be 1D or 2D array")
        
        # Check minimum duration
        min_samples = int(0.5 * self.sampling_rate)  # 0.5 second minimum
        actual_samples = signal.shape[-1] if signal.ndim == 2 else len(signal)
        
        if actual_samples < min_samples:
            raise ValueError(f"{self.name}: Signal too short (minimum 0.5 seconds required)")
            
        return True
    
    def _add_flag(self, flag_type: str, severity: str, message: str, 
                  timestamp: Optional[float] = None):
        """
        Add quality flag with structured information
        
        Parameters:
        - flag_type: Type of flag (e.g., 'ARTIFACT', 'CLIPPING', 'NOISE')
        - severity: Severity level ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')
        - message: Descriptive message
        - timestamp: Time in seconds where issue occurs (optional)
        """
        flag = {
            'type': flag_type,
            'severity': severity,
            'message': message,
            'timestamp': timestamp,
            'module': self.name
        }
        self.flags.append(flag)
        
        # Log the flag
        log_level = {
            'LOW': logging.INFO,
            'MEDIUM': logging.WARNING,
            'HIGH': logging.WARNING,
            'CRITICAL': logging.ERROR
        }.get(severity, logging.INFO)
        
        self.logger.log(log_level, f"{flag_type}: {message}")
    
    def _add_visualization(self, plot_type: str, data: dict, config: dict = None):
        """
        Add visualization specification for report generation
        
        Parameters:
        - plot_type: Type of plot ('time_series', 'spectrum', 'heatmap', etc.)
        - data: Data required for plotting
        - config: Plot configuration options
        """
        viz = {
            'type': plot_type,
            'data': data,
            'config': config or {},
            'module': self.name
        }
        self.visualizations.append(viz)
```

---

## 2. ENHANCED RESULTS AGGREGATOR

### File: `src/core/aggregator.py`

### Purpose
Intelligently combine results from all modules into a comprehensive quality assessment with weighted scoring and detailed reporting.

```python
import numpy as np
from typing import Dict, List, Any, Optional
import json
from datetime import datetime
import logging

class ResultsAggregator:
    """Advanced results aggregation system for EEG quality assessment"""
    
    def __init__(self, module_weights: Dict[str, float] = None):
        self.module_weights = module_weights or self._get_default_weights()
        self.module_results = {}
        self.overall_score = None
        self.overall_grade = None
        self.critical_flags = []
        self.all_flags = []
        self.recommendations = []
        self.processing_stats = {}
        
        self.logger = logging.getLogger("EEG.Aggregator")
        
    def _get_default_weights(self) -> Dict[str, float]:
        """Default weighting scheme for different quality modules"""
        return {
            "signal_integrity": 0.25,      # High weight - fundamental
            "artifact_detection": 0.25,    # High weight - critical for analysis
            "spectral_analysis": 0.20,     # Important for EEG interpretation
            "temporal_consistency": 0.15,  # Moderate weight - stability check
            "complexity_entropy": 0.10,    # Lower weight - advanced metric
            "channel_correlation": 0.03,   # Variable based on channel count
            "impedance_contact": 0.02      # Hardware-specific, lower weight
        }
    
    def add_module_result(self, module_result: Dict[str, Any]):
        """
        Add result from a single quality assessment module
        
        Parameters:
        - module_result: Standardized module result dictionary
        """
        module_name = module_result["module_name"]
        self.module_results[module_name] = module_result
        
        # Collect and categorize flags
        flags = module_result.get("flags", [])
        self.all_flags.extend(flags)
        
        # Identify critical flags
        critical_flags = [f for f in flags if f.get('severity') == 'CRITICAL']
        self.critical_flags.extend(critical_flags)
        
        # Collect recommendations
        recommendations = module_result.get("recommendations", [])
        self.recommendations.extend(recommendations)
        
        # Track processing statistics
        self.processing_stats[module_name] = {
            'execution_time': module_result.get('execution_time', 0),
            'flags_count': len(flags),
            'critical_flags_count': len(critical_flags)
        }
        
        self.logger.info(f"Added results from {module_name}: "
                        f"Score={module_result.get('quality_score', 0):.1f}, "
                        f"Flags={len(flags)}")
    
    def calculate_overall_score(self, signal_metadata: dict = None) -> float:
        """
        Calculate weighted overall quality score with adaptive weighting
        
        Parameters:
        - signal_metadata: Signal information for adaptive weighting
        
        Returns:
        - overall_score: Weighted quality score (0-100)
        """
        if not self.module_results:
            self.overall_score = 0.0
            return self.overall_score
        
        # Adapt weights based on signal characteristics
        adapted_weights = self._adapt_weights(signal_metadata)
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for module_name, result in self.module_results.items():
            # Map module names to weight keys
            weight_key = self._get_weight_key(module_name)
            
            if weight_key in adapted_weights:
                weight = adapted_weights[weight_key]
                score = result.get("quality_score", 0)
                
                # Apply penalty for critical flags
                critical_penalty = len([f for f in result.get("flags", []) 
                                      if f.get('severity') == 'CRITICAL']) * 10
                adjusted_score = max(0, score - critical_penalty)
                
                weighted_sum += weight * adjusted_score
                total_weight += weight
        
        self.overall_score = weighted_sum / total_weight if total_weight > 0 else 0.0
        
        self.logger.info(f"Calculated overall score: {self.overall_score:.1f}")
        return self.overall_score
    
    def _adapt_weights(self, signal_metadata: dict = None) -> Dict[str, float]:
        """
        Adapt module weights based on signal characteristics
        
        Parameters:
        - signal_metadata: Signal information (channel_count, duration, etc.)
        
        Returns:
        - adapted_weights: Adjusted weight dictionary
        """
        weights = self.module_weights.copy()
        
        if signal_metadata:
            channel_count = signal_metadata.get('channel_count', 1)
            duration = signal_metadata.get('duration_seconds', 0)
            
            # Adjust channel correlation weight based on channel count
            if channel_count == 1:
                # Redistribute channel correlation weight to other modules
                correlation_weight = weights.get('channel_correlation', 0)
                weights['channel_correlation'] = 0
                weights['signal_integrity'] += correlation_weight * 0.5
                weights['artifact_detection'] += correlation_weight * 0.5
            elif channel_count > 32:
                # Increase channel correlation importance for high-density EEG
                weights['channel_correlation'] *= 2.0
            
            # Adjust temporal consistency weight for short signals
            if duration < 30:  # Less than 30 seconds
                temp_weight = weights.get('temporal_consistency', 0)
                weights['temporal_consistency'] = temp_weight * 0.5
                weights['spectral_analysis'] += temp_weight * 0.5
        
        # Normalize weights to sum to 1.0
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v/total_weight for k, v in weights.items()}
        
        return weights
    
    def _get_weight_key(self, module_name: str) -> str:
        """Map module names to weight dictionary keys"""
        mapping = {
            "Signal Integrity Assessment": "signal_integrity",
            "Artifact Detection": "artifact_detection",
            "Spectral Analysis": "spectral_analysis", 
            "Temporal Consistency": "temporal_consistency",
            "Complexity and Entropy": "complexity_entropy",
            "Channel Correlation Analysis": "channel_correlation",
            "Impedance and Contact Quality": "impedance_contact"
        }
        return mapping.get(module_name, module_name.lower().replace(" ", "_"))
    
    def assign_grade(self) -> str:
        """
        Assign letter grade based on overall score and critical issues
        
        Returns:
        - grade: Letter grade (A, B, C, D, F)
        """
        if self.overall_score is None:
            self.calculate_overall_score()
        
        # Base grade thresholds
        base_grade = self._score_to_grade(self.overall_score)
        
        # Downgrade for critical issues
        critical_count = len(self.critical_flags)
        if critical_count > 0:
            if base_grade == 'A':
                base_grade = 'B'
            elif base_grade == 'B':
                base_grade = 'C'
            elif base_grade == 'C':
                base_grade = 'D'
            elif base_grade == 'D':
                base_grade = 'F'
        
        self.overall_grade = base_grade
        return self.overall_grade
    
    def _score_to_grade(self, score: float) -> str:
        """Convert numeric score to letter grade"""
        if score >= 90:
            return "A"
        elif score >= 80:
            return "B"
        elif score >= 70:
            return "C"
        elif score >= 60:
            return "D"
        else:
            return "F"
    
    def identify_critical_issues(self) -> List[dict]:
        """
        Identify and categorize critical quality issues
        
        Returns:
        - critical_issues: List of critical issue dictionaries
        """
        self.critical_flags = [
            flag for flag in self.all_flags 
            if flag.get('severity') == 'CRITICAL'
        ]
        
        # Group by issue type
        issue_groups = {}
        for flag in self.critical_flags:
            issue_type = flag.get('type', 'UNKNOWN')
            if issue_type not in issue_groups:
                issue_groups[issue_type] = []
            issue_groups[issue_type].append(flag)
        
        # Create summary of critical issues
        critical_summary = []
        for issue_type, flags in issue_groups.items():
            critical_summary.append({
                'type': issue_type,
                'count': len(flags),
                'description': self._get_issue_description(issue_type),
                'flags': flags
            })
        
        return critical_summary
    
    def _get_issue_description(self, issue_type: str) -> str:
        """Get human-readable description for issue types"""
        descriptions = {
            'CLIPPING': 'Signal amplitude saturation detected',
            'FLAT_SIGNAL': 'Signal shows no variation (flat line)',
            'HIGH_IMPEDANCE': 'Poor electrode contact quality',
            'ELECTRODE_POP': 'Sudden electrode disconnection/reconnection',
            'BAD_CHANNEL': 'Channel shows abnormal characteristics',
            'UNSTABLE_CONNECTION': 'Connection quality varies over time',
            'EXCESSIVE_ARTIFACTS': 'High levels of contaminating artifacts',
            'POWER_LINE_NOISE': 'Strong electrical interference detected'
        }
        return descriptions.get(issue_type, f'Quality issue: {issue_type}')
    
    def prioritize_recommendations(self) -> Dict[str, List[str]]:
        """
        Prioritize and categorize recommendations by urgency and impact
        
        Returns:
        - categorized_recommendations: Dictionary with priority categories
        """
        # Remove duplicates while preserving order
        unique_recommendations = []
        seen = set()
        
        for rec in self.recommendations:
            if rec not in seen:
                unique_recommendations.append(rec)
                seen.add(rec)
        
        # Categorize by priority keywords
        categories = {
            'immediate': [],      # Critical issues requiring immediate action
            'hardware': [],       # Hardware/setup related improvements
            'data_collection': [], # Data acquisition improvements
            'analysis': []        # Analysis parameter adjustments
        }
        
        priority_keywords = {
            'immediate': ['impedance', 'contact', 'electrode', 'clipping', 'saturated', 
                         'disconnected', 'critical', 'urgent'],
            'hardware': ['cable', 'ground', 'reference', 'amplifier', 'setup', 'placement'],
            'data_collection': ['filter', 'sampling', 'duration', 'environment', 
                               'preparation', 'recording'],
            'analysis': ['parameter', 'threshold', 'algorithm', 'processing', 'consider']
        }
        
        for rec in unique_recommendations:
            rec_lower = rec.lower()
            categorized = False
            
            # Check each category in priority order
            for category in ['immediate', 'hardware', 'data_collection', 'analysis']:
                if any(keyword in rec_lower for keyword in priority_keywords[category]):
                    categories[category].append(rec)
                    categorized = True
                    break
            
            # Default to analysis category if no keywords match
            if not categorized:
                categories['analysis'].append(rec)
        
        return categories
    
    def generate_summary_report(self, signal_metadata: dict = None) -> Dict[str, Any]:
        """
        Generate comprehensive summary report with all aggregated results
        
        Parameters:
        - signal_metadata: Signal information for adaptive scoring
        
        Returns:
        - summary_report: Complete assessment summary
        """
        # Calculate final scores and grades
        self.calculate_overall_score(signal_metadata)
        self.assign_grade()
        
        # Analyze issues and recommendations
        critical_issues = self.identify_critical_issues()
        categorized_recommendations = self.prioritize_recommendations()
        
        # Generate comprehensive summary
        summary = {
            "timestamp": datetime.now().isoformat(),
            "assessment_metadata": {
                "total_modules_run": len(self.module_results),
                "total_processing_time": sum(
                    stats['execution_time'] for stats in self.processing_stats.values()
                ),
                "signal_metadata": signal_metadata or {}
            },
            "overall_assessment": {
                "quality_score": round(self.overall_score, 1),
                "quality_grade": self.overall_grade,
                "assessment_level": self._get_assessment_level()
            },
            "quality_issues": {
                "critical_issues": critical_issues,
                "total_flags": len(self.all_flags),
                "flag_distribution": self._get_flag_distribution()
            },
            "module_performance": {
                "scores": {
                    name: result.get("quality_score", 0) 
                    for name, result in self.module_results.items()
                },
                "execution_times": {
                    name: stats['execution_time'] 
                    for name, stats in self.processing_stats.items()
                }
            },
            "recommendations": categorized_recommendations,
            "detailed_results": self.module_results
        }
        
        self.logger.info(f"Generated summary report: Score={self.overall_score:.1f}, "
                        f"Grade={self.overall_grade}, Issues={len(critical_issues)}")
        
        return summary
    
    def _get_assessment_level(self) -> str:
        """Get qualitative assessment level based on score and issues"""
        if self.critical_flags:
            return "Poor - Critical Issues Detected"
        elif self.overall_score >= 90:
            return "Excellent"
        elif self.overall_score >= 80:
            return "Good"
        elif self.overall_score >= 70:
            return "Acceptable"
        elif self.overall_score >= 60:
            return "Marginal"
        else:
            return "Poor"
    
    def _get_flag_distribution(self) -> Dict[str, int]:
        """Get distribution of flags by severity level"""
        distribution = {'LOW': 0, 'MEDIUM': 0, 'HIGH': 0, 'CRITICAL': 0}
        
        for flag in self.all_flags:
            severity = flag.get('severity', 'MEDIUM')
            if severity in distribution:
                distribution[severity] += 1
        
        return distribution
```

---

## 3. ENHANCED CONFIGURATION MANAGER

### File: `src/core/config_manager.py`

### Purpose
Centralized, flexible configuration management with YAML/JSON support, validation, and environment-specific settings.

```python
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
import os
import logging
from dataclasses import dataclass

@dataclass
class ProcessingConfig:
    """Processing configuration parameters"""
    segment_length_sec: float = 30.0
    overlap_percentage: float = 50.0
    min_signal_length_sec: float = 10.0
    max_workers: int = 4
    enable_parallel: bool = True

@dataclass
class OutputConfig:
    """Output configuration parameters"""
    default_format: str = 'both'  # 'pdf', 'json', 'both'
    plot_dpi: int = 300
    save_plots: bool = True
    output_directory: str = 'output'
    report_template: str = 'standard'

class ConfigManager:
    """Advanced configuration management system"""
    
    def __init__(self, config_file: Optional[str] = None, 
                 environment: str = 'default'):
        self.config_file = config_file
        self.environment = environment
        self.config = self._load_default_config()
        
        # Setup logging
        self.logger = logging.getLogger("EEG.Config")
        
        # Load user configuration if provided
        if config_file and Path(config_file).exists():
            self._load_user_config(config_file)
        elif config_file:
            self.logger.warning(f"Config file not found: {config_file}")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load comprehensive default configuration"""
        return {
            "general": {
                "version": "1.0.0",
                "default_segment_length": 30.0,  # seconds
                "overlap_ratio": 0.5,
                "output_directory": "output",
                "temp_directory": "temp",
                "log_level": "INFO",
                "supported_formats": [".edf", ".bdf", ".fif", ".set", ".cnt"],
                "max_file_size_mb": 1000,
                "default_sampling_rate": 1000
            },
            
            "processing": {
                "parallel_processing": True,
                "max_workers": 4,
                "chunk_size": 1000,  # samples per chunk
                "memory_limit_gb": 8,
                "enable_preprocessing": True,
                "preprocessing_steps": ["detrend", "notch_filter"]
            },
            
            "signal_processing": {
                "filter_settings": {
                    "lowpass_freq": 100.0,
                    "highpass_freq": 0.5,
                    "notch_frequencies": [50, 60],
                    "filter_order": 4,
                    "filter_method": "butterworth"
                },
                "artifact_rejection": {
                    "enable_automatic": True,
                    "amplitude_threshold": 150.0,  # microvolts
                    "gradient_threshold": 75.0
                }
            },
            
            "quality_thresholds": {
                "signal_integrity": {
                    "snr_threshold_db": 10.0,
                    "dynamic_range_min_uv": 10.0,
                    "clipping_threshold_percent": 1.0,
                    "saturation_threshold_percent": 95.0,
                    "flat_signal_threshold_uv": 0.1,
                    "dc_offset_max_uv": 100.0,
                    "bit_depth_utilization_min": 0.1
                },
                
                "artifact_detection": {
                    "eog_threshold_uv": 150.0,
                    "emg_frequency_range": [30, 100],
                    "emg_threshold_factor": 3.0,
                    "motion_threshold_uv_per_sec": 200.0,
                    "electrode_pop_threshold": 5.0,
                    "powerline_tolerance_db": -20.0,
                    "muscle_contamination_threshold": 0.3
                },
                
                "spectral_analysis": {
                    "frequency_bands": {
                        "delta": [0.5, 4],
                        "theta": [4, 8],
                        "alpha": [8, 13],
                        "beta": [13, 30],
                        "gamma": [30, 100]
                    },
                    "psd_method": "welch",
                    "nperseg_factor": 4,
                    "minimum_snr_db": 10,
                    "spectral_slope_range": [-2, -0.5],
                    "alpha_peak_prominence": 2.0,
                    "line_noise_detection_threshold": 3.0
                },
                
                "temporal_consistency": {
                    "stationarity_window_sec": 2.0,
                    "stationarity_threshold": 0.05,
                    "variance_stability_threshold": 0.3,
                    "trend_significance_threshold": 0.05,
                    "autocorr_max_lag": 100,
                    "drift_threshold_uv_per_min": 5.0
                },
                
                "complexity_entropy": {
                    "sample_entropy": {
                        "m": 2,
                        "r_factor": 0.2,
                        "optimal_range": [0.5, 2.0]
                    },
                    "approximate_entropy": {
                        "m": 2,
                        "r_factor": 0.2,
                        "optimal_range": [0.4, 1.5]
                    },
                    "lempel_ziv": {
                        "normalize": True,
                        "optimal_range": [0.3, 0.8]
                    },
                    "spectral_entropy": {
                        "optimal_range": [0.6, 0.9]
                    },
                    "hjorth_parameters": {
                        "complexity_range": [1.2, 2.5],
                        "mobility_range": [0.5, 2.0]
                    }
                },
                
                "channel_correlation": {
                    "min_correlation_threshold": 0.1,
                    "max_correlation_threshold": 0.95,
                    "coherence_frequency_bands": {
                        "delta": [0.5, 4],
                        "theta": [4, 8], 
                        "alpha": [8, 13],
                        "beta": [13, 30]
                    },
                    "bad_channel_threshold": 0.2,
                    "spatial_consistency_threshold": 0.3
                },
                
                "impedance_contact": {
                    "impedance_categories": {
                        "excellent": 5,     # kOhm
                        "good": 10,
                        "acceptable": 25,
                        "poor": 50,
                        "unacceptable": 100
                    },
                    "contact_quality_indicators": {
                        "noise_level_threshold": 10.0,  # uV RMS
                        "stability_threshold": 5.0,     # % variation
                        "drift_threshold": 10.0         # uV/min
                    }
                }
            },
            
            "module_weights": {
                "signal_integrity": 0.25,
                "artifact_detection": 0.25,
                "spectral_analysis": 0.20,
                "temporal_consistency": 0.15,
                "complexity_entropy": 0.10,
                "channel_correlation": 0.03,
                "impedance_contact": 0.02
            },
            
            "grading": {
                "score_thresholds": {
                    "A": 90,
                    "B": 80,
                    "C": 70,
                    "D": 60,
                    "F": 0
                },
                "critical_flag_penalty": 10,  # points deducted per critical flag
                "flag_weight_multipliers": {
                    "CRITICAL": 1.0,
                    "HIGH": 0.5,
                    "MEDIUM": 0.2,
                    "LOW": 0.1
                }
            },
            
            "visualization": {
                "plot_settings": {
                    "style": "publication",
                    "color_scheme": "colorblind_friendly",
                    "dpi": 300,
                    "figure_size": [12, 8],
                    "font_size": 10,
                    "line_width": 1.5
                },
                "time_series": {
                    "max_channels_display": 16,
                    "time_window_sec": 10.0,
                    "amplitude_scaling": "auto",
                    "show_events": True
                },
                "spectral": {
                    "frequency_range": [0.5, 100],
                    "log_scale": False,
                    "show_frequency_bands": True,
                    "psd_smoothing": True
                },
                "report_format": {
                    "template": "standard",
                    "include_raw_data": False,
                    "include_processing_details": True,
                    "max_plots_per_page": 4
                }
            },
            
            "output": {
                "formats": ["pdf", "json"],
                "save_intermediate_results": True,
                "compress_large_files": True,
                "timestamp_format": "%Y%m%d_%H%M%S",
                "filename_template": "eeg_quality_{timestamp}_{signal_name}",
                "backup_results": False
            },
            
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_logging": True,
                "console_logging": True,
                "max_file_size_mb": 10,
                "backup_count": 5
            }
        }
    
    def _load_user_config(self, config_file: str):
        """
        Load and merge user configuration with defaults
        
        Parameters:
        - config_file: Path to user configuration file
        """
        try:
            with open(config_file, 'r') as f:
                if config_file.endswith('.json'):
                    user_config = json.load(f)
                elif config_file.endswith(('.yml', '.yaml')):
                    user_config = yaml.safe_load(f)
                else:
                    raise ValueError("Config file must be JSON or YAML format")
            
            # Validate user configuration
            self._validate_config(user_config)
            
            # Deep merge with defaults
            self.config = self._deep_merge(self.config, user_config)
            
            self.logger.info(f"Loaded user configuration from {config_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to load user config {config_file}: {e}")
            self.logger.info("Continuing with default configuration")
    
    def _validate_config(self, config: Dict[str, Any]):
        """
        Validate configuration parameters
        
        Parameters:
        - config: Configuration dictionary to validate
        
        Raises:
        - ValueError: If configuration contains invalid values
        """
        # Validate critical parameters
        if 'module_weights' in config:
            weights = config['module_weights']
            if not isinstance(weights, dict):
                raise ValueError("module_weights must be a dictionary")
            
            weight_sum = sum(weights.values())
            if not (0.8 <= weight_sum <= 1.2):  # Allow some flexibility
                self.logger.warning(f"Module weights sum to {weight_sum:.3f}, "
                                  "consider normalizing to 1.0")
        
        # Validate frequency bands
        if 'quality_thresholds' in config and 'spectral_analysis' in config['quality_thresholds']:
            spectral_config = config['quality_thresholds']['spectral_analysis']
            if 'frequency_bands' in spectral_config:
                bands = spectral_config['frequency_bands']
                for band_name, (low, high) in bands.items():
                    if low >= high:
                        raise ValueError(f"Invalid frequency band {band_name}: "
                                       f"low freq ({low}) >= high freq ({high})")
        
        # Validate processing parameters
        if 'processing' in config:
            proc_config = config['processing']
            if 'max_workers' in proc_config:
                max_workers = proc_config['max_workers']
                if not isinstance(max_workers, int) or max_workers < 1:
                    raise ValueError("max_workers must be positive integer")
    
    def _deep_merge(self, base: dict, override: dict) -> dict:
        """
        Recursively merge configuration dictionaries
        
        Parameters:
        - base: Base configuration dictionary
        - override: Override configuration dictionary
        
        Returns:
        - merged: Deep-merged configuration
        """
        result = base.copy()
        
        for key, value in override.items():
            if (key in result and 
                isinstance(result[key], dict) and 
                isinstance(value, dict)):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get_module_config(self, module_name: str) -> Dict[str, Any]:
        """
        Get configuration for specific quality assessment module
        
        Parameters:
        - module_name: Name of the module
        
        Returns:
        - module_config: Configuration dictionary for the module
        """
        # Map module names to config keys
        module_mapping = {
            "signal_integrity": ["quality_thresholds", "signal_integrity"],
            "artifact_detection": ["quality_thresholds", "artifact_detection"],
            "spectral_analysis": ["quality_thresholds", "spectral_analysis"],
            "temporal_consistency": ["quality_thresholds", "temporal_consistency"],
            "complexity_entropy": ["quality_thresholds", "complexity_entropy"],
            "channel_correlation": ["quality_thresholds", "channel_correlation"],
            "impedance_contact": ["quality_thresholds", "impedance_contact"]
        }
        
        # Normalize module name
        normalized_name = module_name.lower().replace(" ", "_")
        
        if normalized_name in module_mapping:
            config_path = module_mapping[normalized_name]
            config = self.config
            
            # Navigate to nested configuration
            for key in config_path:
                config = config.get(key, {})
            
            return config
        
        self.logger.warning(f"No configuration found for module: {module_name}")
        return {}
    
    def get_processing_config(self) -> ProcessingConfig:
        """Get processing configuration as dataclass"""
        proc_config = self.config.get("processing", {})
        general_config = self.config.get("general", {})
        
        return ProcessingConfig(
            segment_length_sec=general_config.get("default_segment_length", 30.0),
            overlap_percentage=general_config.get("overlap_ratio", 0.5) * 100,
            min_signal_length_sec=10.0,
            max_workers=proc_config.get("max_workers", 4),
            enable_parallel=proc_config.get("parallel_processing", True)
        )
    
    def get_output_config(self) -> OutputConfig:
        """Get output configuration as dataclass"""
        output_config = self.config.get("output", {})
        viz_config = self.config.get("visualization", {})
        
        return OutputConfig(
            default_format=output_config.get("formats", ["pdf", "json"])[0],
            plot_dpi=viz_config.get("plot_settings", {}).get("dpi", 300),
            save_plots=True,
            output_directory=self.config.get("general", {}).get("output_directory", "output"),
            report_template=viz_config.get("report_format", {}).get("template", "standard")
        )
    
    def get_module_weights(self) -> Dict[str, float]:
        """Get module weighting configuration"""
        return self.config.get("module_weights", {})
    
    def get_visualization_config(self) -> Dict[str, Any]:
        """Get visualization configuration"""
        return self.config.get("visualization", {})
    
    def get_threshold(self, module: str, parameter: str, default: Any = None) -> Any:
        """
        Get specific threshold value with fallback to default
        
        Parameters:
        - module: Module name
        - parameter: Parameter name
        - default: Default value if not found
        
        Returns:
        - threshold_value: Configuration value or default
        """
        module_config = self.get_module_config(module)
        return module_config.get(parameter, default)
    
    def update_config(self, key_path: str, value: Any):
        """
        Update configuration value using dot notation
        
        Parameters:
        - key_path: Dot-separated path (e.g., 'processing.max_workers')
        - value: New value to set
        """
        keys = key_path.split('.')
        current = self.config
        
        # Navigate to parent dictionary
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # Set the value
        current[keys[-1]] = value
        self.logger.info(f"Updated configuration: {key_path} = {value}")
    
    def save_config(self, output_file: str):
        """
        Save current configuration to file
        
        Parameters:
        - output_file: Path to output configuration file
        """
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(output_file, 'w') as f:
                if output_file.endswith('.json'):
                    json.dump(self.config, f, indent=2, sort_keys=True)
                elif output_file.endswith(('.yml', '.yaml')):
                    yaml.dump(self.config, f, default_flow_style=False, 
                            indent=2, sort_keys=True)
                else:
                    raise ValueError("Output file must have .json, .yml, or .yaml extension")
            
            self.logger.info(f"Configuration saved to {output_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            raise
    
    def export_template_config(self, output_file: str):
        """
        Export a template configuration file with comments
        
        Parameters:
        - output_file: Path to template configuration file
        """
        template = {
            "_description": "EEG Quality Assessment Configuration Template",
            "_version": self.config["general"]["version"],
            
            "processing": {
                "_comment": "Processing and performance settings",
                "parallel_processing": True,
                "max_workers": 4,
                "memory_limit_gb": 8
            },
            
            "module_weights": {
                "_comment": "Relative importance of each quality module (should sum to ~1.0)",
                "signal_integrity": 0.25,
                "artifact_detection": 0.25,
                "spectral_analysis": 0.20,
                "temporal_consistency": 0.15,
                "complexity_entropy": 0.10,
                "channel_correlation": 0.03,
                "impedance_contact": 0.02
            },
            
            "quality_thresholds": {
                "_comment": "Adjust these thresholds based on your specific requirements",
                "signal_integrity": {
                    "snr_threshold_db": 10.0,
                    "clipping_threshold_percent": 1.0
                },
                "artifact_detection": {
                    "eog_threshold_uv": 150.0,
                    "emg_threshold_factor": 3.0
                }
            },
            
            "visualization": {
                "_comment": "Report and plot generation settings",
                "plot_settings": {
                    "dpi": 300,
                    "style": "publication"
                }
            }
        }
        
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w') as f:
            if output_file.endswith('.json'):
                json.dump(template, f, indent=2)
            else:
                yaml.dump(template, f, default_flow_style=False, indent=2)
        
        self.logger.info(f"Template configuration exported to {output_file}")

---

## 4. SIGNAL LOADER

### File: `src/core/signal_loader.py`

### Purpose
Multi-format EEG signal loading with standardized output format and comprehensive metadata extraction.

```python
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
import logging

try:
    import mne
    MNE_AVAILABLE = True
except ImportError:
    MNE_AVAILABLE = False

try:
    import pyedflib
    PYEDF_AVAILABLE = True
except ImportError:
    PYEDF_AVAILABLE = False

class SignalLoader:
    """Multi-format EEG signal loader with standardized output"""
    
    def __init__(self):
        self.logger = logging.getLogger("EEG.SignalLoader")
        self.supported_formats = self._get_supported_formats()
        
    def _get_supported_formats(self) -> List[str]:
        """Get list of supported file formats based on available libraries"""
        formats = ['.npy', '.npz']  # Always supported
        
        if MNE_AVAILABLE:
            formats.extend(['.fif', '.set', '.vhdr', '.cnt'])
        
        if PYEDF_AVAILABLE:
            formats.extend(['.edf', '.bdf'])
        
        return formats
    
    def load_signal(self, 
                   file_path: Union[str, Path],
                   **kwargs) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Load EEG signal from file with automatic format detection
        
        Parameters:
        - file_path: Path to signal file
        - **kwargs: Format-specific loading parameters
        
        Returns:
        - signal: EEG data array (channels x samples)
        - metadata: Signal metadata dictionary
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Signal file not found: {file_path}")
        
        # Check file size
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > 1000:  # 1GB limit
            self.logger.warning(f"Large file detected: {file_size_mb:.1f}MB")
        
        # Determine format and load
        file_extension = file_path.suffix.lower()
        
        if file_extension == '.edf':
            return self._load_edf(file_path, **kwargs)
        elif file_extension == '.bdf':
            return self._load_bdf(file_path, **kwargs)
        elif file_extension == '.fif':
            return self._load_fif(file_path, **kwargs)
        elif file_extension == '.set':
            return self._load_set(file_path, **kwargs)
        elif file_extension == '.cnt':
            return self._load_cnt(file_path, **kwargs)
        elif file_extension == '.vhdr':
            return self._load_brainvision(file_path, **kwargs)
        elif file_extension in ['.npy', '.npz']:
            return self._load_numpy(file_path, **kwargs)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}")
    
    def _load_edf(self, file_path: Path, **kwargs) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Load EDF format using pyedflib"""
        if not PYEDF_AVAILABLE:
            raise ImportError("pyedflib required for EDF files")
        
        try:
            with pyedflib.EdfReader(str(file_path)) as f:
                n_channels = f.signals_in_file
                sampling_rates = [f.getSampleFrequency(i) for i in range(n_channels)]
                channel_names = [f.getLabel(i) for i in range(n_channels)]
                
                # Check if all channels have same sampling rate
                if len(set(sampling_rates)) > 1:
                    self.logger.warning("Different sampling rates detected, using first channel rate")
                
                sampling_rate = sampling_rates[0]
                
                # Load signal data
                signal_data = []
                for i in range(n_channels):
                    channel_data = f.readSignal(i)
                    signal_data.append(channel_data)
                
                signal = np.array(signal_data)
                
                # Extract metadata
                metadata = {
                    'sampling_rate': sampling_rate,
                    'channel_names': channel_names,
                    'n_channels': n_channels,
                    'duration_seconds': signal.shape[1] / sampling_rate,
                    'file_format': 'EDF',
                    'file_path': str(file_path),
                    'recording_info': {
                        'patient_id': f.getPatientName(),
                        'recording_id': f.getRecordingAdditional(),
                        'start_time': f.getStartdatetime()
                    }
                }
                
                self.logger.info(f"Loaded EDF: {n_channels} channels, "
                               f"{sampling_rate}Hz, {metadata['duration_seconds']:.1f}s")
                
                return signal, metadata
                
        except Exception as e:
            raise ValueError(f"Failed to load EDF file: {e}")
    
    def _load_bdf(self, file_path: Path, **kwargs) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Load BDF format using pyedflib"""
        # BDF loading is similar to EDF
        return self._load_edf(file_path, **kwargs)
    
    def _load_fif(self, file_path: Path, **kwargs) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Load FIF format using MNE"""
        if not MNE_AVAILABLE:
            raise ImportError("MNE-Python required for FIF files")
        
        try:
            # Load raw data
            raw = mne.io.read_raw_fif(str(file_path), preload=True, verbose=False)
            
            # Get EEG channels only
            eeg_picks = mne.pick_types(raw.info, eeg=True, exclude='bads')
            
            if len(eeg_picks) == 0:
                # Fallback to all channels if no EEG channels marked
                eeg_picks = range(len(raw.ch_names))
                self.logger.warning("No EEG channels found, using all channels")
            
            # Extract signal and metadata
            signal = raw.get_data(picks=eeg_picks)
            channel_names = [raw.ch_names[i] for i in eeg_picks]
            
            metadata = {
                'sampling_rate': raw.info['sfreq'],
                'channel_names': channel_names,
                'n_channels': len(channel_names),
                'duration_seconds': raw.times[-1],
                'file_format': 'FIF',
                'file_path': str(file_path),
                'recording_info': {
                    'measurement_date': raw.info.get('meas_date'),
                    'experimenter': raw.info.get('experimenter'),
                    'description': raw.info.get('description')
                }
            }
            
            self.logger.info(f"Loaded FIF: {len(channel_names)} channels, "
                           f"{raw.info['sfreq']}Hz, {metadata['duration_seconds']:.1f}s")
            
            return signal, metadata
            
        except Exception as e:
            raise ValueError(f"Failed to load FIF file: {e}")
    
    def _load_set(self, file_path: Path, **kwargs) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Load EEGLAB SET format using MNE"""
        if not MNE_AVAILABLE:
            raise ImportError("MNE-Python required for SET files")
        
        try:
            raw = mne.io.read_raw_eeglab(str(file_path), preload=True, verbose=False)
            
            signal = raw.get_data()
            
            metadata = {
                'sampling_rate': raw.info['sfreq'],
                'channel_names': raw.ch_names,
                'n_channels': len(raw.ch_names),
                'duration_seconds': raw.times[-1],
                'file_format': 'SET',
                'file_path': str(file_path)
            }
            
            self.logger.info(f"Loaded SET: {len(raw.ch_names)} channels, "
                           f"{raw.info['sfreq']}Hz, {metadata['duration_seconds']:.1f}s")
            
            return signal, metadata
            
        except Exception as e:
            raise ValueError(f"Failed to load SET file: {e}")
    
    def _load_cnt(self, file_path: Path, **kwargs) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Load Neuroscan CNT format using MNE"""
        if not MNE_AVAILABLE:
            raise ImportError("MNE-Python required for CNT files")
        
        try:
            raw = mne.io.read_raw_cnt(str(file_path), preload=True, verbose=False)
            
            signal = raw.get_data()
            
            metadata = {
                'sampling_rate': raw.info['sfreq'],
                'channel_names': raw.ch_names,
                'n_channels': len(raw.ch_names),
                'duration_seconds': raw.times[-1],
                'file_format': 'CNT',
                'file_path': str(file_path)
            }
            
            return signal, metadata
            
        except Exception as e:
            raise ValueError(f"Failed to load CNT file: {e}")
    
    def _load_brainvision(self, file_path: Path, **kwargs) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Load BrainVision format using MNE"""
        if not MNE_AVAILABLE:
            raise ImportError("MNE-Python required for BrainVision files")
        
        try:
            raw = mne.io.read_raw_brainvision(str(file_path), preload=True, verbose=False)
            
            signal = raw.get_data()
            
            metadata = {
                'sampling_rate': raw.info['sfreq'],
                'channel_names': raw.ch_names,
                'n_channels': len(raw.ch_names),
                'duration_seconds': raw.times[-1],
                'file_format': 'BrainVision',
                'file_path': str(file_path)
            }
            
            return signal, metadata
            
        except Exception as e:
            raise ValueError(f"Failed to load BrainVision file: {e}")
    
    def _load_numpy(self, file_path: Path, **kwargs) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Load NumPy format (.npy or .npz)"""
        try:
            if file_path.suffix == '.npy':
                signal = np.load(file_path)
            else:  # .npz
                data = np.load(file_path)
                if 'signal' in data:
                    signal = data['signal']
                else:
                    # Use first array in file
                    signal = data[list(data.keys())[0]]
            
            # Ensure 2D array (channels x samples)
            if signal.ndim == 1:
                signal = signal.reshape(1, -1)
            
            # Extract metadata from kwargs or use defaults
            sampling_rate = kwargs.get('sampling_rate', 1000.0)
            channel_names = kwargs.get('channel_names', 
                                     [f'Ch{i+1}' for i in range(signal.shape[0])])
            
            metadata = {
                'sampling_rate': sampling_rate,
                'channel_names': channel_names,
                'n_channels': signal.shape[0],
                'duration_seconds': signal.shape[1] / sampling_rate,
                'file_format': 'NumPy',
                'file_path': str(file_path)
            }
            
            self.logger.info(f"Loaded NumPy: {signal.shape[0]} channels, "
                           f"{sampling_rate}Hz, {metadata['duration_seconds']:.1f}s")
            
            return signal, metadata
            
        except Exception as e:
            raise ValueError(f"Failed to load NumPy file: {e}")
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats"""
        return self.supported_formats.copy()
    
    def validate_signal_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Validate signal file without loading data
        
        Parameters:
        - file_path: Path to signal file
        
        Returns:
        - validation_info: Dictionary with validation results
        """
        file_path = Path(file_path)
        
        validation = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {}
        }
        
        # Check file existence
        if not file_path.exists():
            validation['valid'] = False
            validation['errors'].append(f"File not found: {file_path}")
            return validation
        
        # Check file extension
        file_extension = file_path.suffix.lower()
        if file_extension not in self.supported_formats:
            validation['valid'] = False
            validation['errors'].append(f"Unsupported format: {file_extension}")
            return validation
        
        # Check file size
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        validation['file_info']['size_mb'] = file_size_mb
        
        if file_size_mb > 1000:
            validation['warnings'].append(f"Large file: {file_size_mb:.1f}MB")
        
        if file_size_mb == 0:
            validation['valid'] = False
            validation['errors'].append("Empty file")
        
        # Format-specific validation
        if file_extension in ['.edf', '.bdf'] and not PYEDF_AVAILABLE:
            validation['valid'] = False
            validation['errors'].append("pyedflib not available for EDF/BDF files")
        
        if file_extension in ['.fif', '.set', '.cnt', '.vhdr'] and not MNE_AVAILABLE:
            validation['valid'] = False
            validation['errors'].append("MNE-Python not available for this format")
        
        return validation

---

## 5. MAIN EEG PROCESSOR (UPDATED)

### File: `src/core/eeg_processor.py`

### Purpose
Updated main orchestrator that coordinates all modules according to the new architecture.

```python
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
import time
import logging
import concurrent.futures
from concurrent.futures import ProcessPoolExecutor, as_completed

from .base_module import BaseQualityModule
from .aggregator import ResultsAggregator
from .config_manager import ConfigManager
from .signal_loader import SignalLoader

# Import all quality assessment modules
from ..modules.signal_integrity import SignalIntegrityModule
from ..modules.artifact_detection import ArtifactDetectionModule  
from ..modules.spectral_analysis import SpectralAnalysisModule
from ..modules.temporal_consistency import TemporalConsistencyModule
from ..modules.complexity_entropy import ComplexityEntropyModule
from ..modules.channel_correlation import ChannelCorrelationModule
from ..modules.impedance_contact import ImpedanceContactModule

from ..utils.validation import InputValidator
from ..visualization.report_generator import ReportGenerator

class EEGProcessor:
    """
    Main EEG quality assessment processor with parallel processing capabilities
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize EEG processor
        
        Parameters:
        - config_file: Optional path to configuration file
        """
        # Initialize core components
        self.config_manager = ConfigManager(config_file)
        self.signal_loader = SignalLoader()
        self.validator = InputValidator()
        
        # Setup logging
        self._setup_logging()
        self.logger = logging.getLogger("EEG.Processor")
        
        # Processing state
        self.signal = None
        self.metadata = {}
        self.modules = {}
        self.aggregator = None
        self.results = {}
        self.processing_time = 0.0
        
        # Initialize modules
        self._initialize_modules()
        
        self.logger.info("EEG Quality Processor initialized")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_config = self.config_manager.config.get("logging", {})
        
        logging.basicConfig(
            level=getattr(logging, log_config.get("level", "INFO")),
            format=log_config.get("format", 
                                "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        )
    
    def _initialize_modules(self):
        """Initialize all quality assessment modules"""
        self.logger.info("Initializing quality assessment modules...")
        
        # Module class mapping
        module_classes = {
            "signal_integrity": SignalIntegrityModule,
            "artifact_detection": ArtifactDetectionModule,
            "spectral_analysis": SpectralAnalysisModule,
            "temporal_consistency": TemporalConsistencyModule,
            "complexity_entropy": ComplexityEntropyModule,
            "channel_correlation": ChannelCorrelationModule,
            "impedance_contact": ImpedanceContactModule
        }
        
        # Initialize each module
        for module_key, module_class in module_classes.items():
            try:
                module_config = self.config_manager.get_module_config(module_key)
                # Note: sampling_rate will be set when signal is loaded
                self.modules[module_key] = module_class(
                    sampling_rate=None,  # Will be updated on signal load
                    config=module_config
                )
                self.logger.debug(f"Initialized {module_key} module")
            except Exception as e:
                self.logger.error(f"Failed to initialize {module_key}: {e}")
        
        self.logger.info(f"Initialized {len(self.modules)} modules")
    
    def load_signal_from_file(self, 
                             file_path: Union[str, Path],
                             **kwargs) -> bool:
        """
        Load EEG signal from file
        
        Parameters:
        - file_path: Path to signal file
        - **kwargs: Additional loading parameters
        
        Returns:
        - success: True if signal loaded successfully
        """
        try:
            # Validate file
            validation = self.signal_loader.validate_signal_file(file_path)
            if not validation['valid']:
                self.logger.error(f"File validation failed: {validation['errors']}")
                return False
            
            # Load signal
            self.logger.info(f"Loading signal from {file_path}")
            self.signal, self.metadata = self.signal_loader.load_signal(file_path, **kwargs)
            
            # Validate loaded signal
            self.validator.validate_signal_data(self.signal, self.metadata)
            
            # Update module sampling rates
            self._update_module_sampling_rates()
            
            # Initialize aggregator with signal metadata
            module_weights = self.config_manager.get_module_weights()
            self.aggregator = ResultsAggregator(module_weights)
            
            self.logger.info(f"Signal loaded successfully: "
                           f"{self.metadata['n_channels']} channels, "
                           f"{self.metadata['sampling_rate']}Hz, "
                           f"{self.metadata['duration_seconds']:.1f}s")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load signal: {e}")
            return False
    
    def load_signal_from_array(self,
                              signal: np.ndarray,
                              sampling_rate: float,
                              channel_names: Optional[List[str]] = None,
                              **metadata_kwargs) -> bool:
        """
        Load EEG signal from numpy array
        
        Parameters:
        - signal: EEG data array (channels x samples)
        - sampling_rate: Sampling frequency in Hz
        - channel_names: Optional channel names
        - **metadata_kwargs: Additional metadata
        
        Returns:
        - success: True if signal loaded successfully
        """
        try:
            # Ensure proper format
            if signal.ndim == 1:
                signal = signal.reshape(1, -1)
            
            # Create metadata
            if channel_names is None:
                channel_names = [f'Ch{i+1}' for i in range(signal.shape[0])]
            
            self.metadata = {
                'sampling_rate': sampling_rate,
                'channel_names': channel_names,
                'n_channels': signal.shape[0],
                'duration_seconds': signal.shape[1] / sampling_rate,
                'file_format': 'Array',
                'file_path': 'memory',
                **metadata_kwargs
            }
            
            # Validate signal
            self.validator.validate_signal_data(signal, self.metadata)
            
            self.signal = signal
            
            # Update module sampling rates
            self._update_module_sampling_rates()
            
            # Initialize aggregator
            module_weights = self.config_manager.get_module_weights()
            self.aggregator = ResultsAggregator(module_weights)
            
            self.logger.info(f"Array signal loaded: "
                           f"{self.metadata['n_channels']} channels, "
                           f"{sampling_rate}Hz, "
                           f"{self.metadata['duration_seconds']:.1f}s")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load signal array: {e}")
            return False
    
    def _update_module_sampling_rates(self):
        """Update sampling rates for all modules"""
        sampling_rate = self.metadata['sampling_rate']
        
        for module in self.modules.values():
            module.sampling_rate = sampling_rate
    
    def run_analysis(self,
                    modules_to_run: Optional[List[str]] = None,
                    segment_length: Optional[float] = None,
                    enable_parallel: bool = None) -> Dict[str, Any]:
        """
        Run quality assessment analysis
        
        Parameters:
        - modules_to_run: List of module names to run (None = all modules)
        - segment_length: Signal segment length in seconds (None = use config)
        - enable_parallel: Enable parallel processing (None = use config)
        
        Returns:
        - results: Complete assessment results
        """
        if self.signal is None:
            raise ValueError("No signal loaded. Call load_signal_from_file() or "
                           "load_signal_from_array() first.")
        
        start_time = time.time()
        
        # Determine processing parameters
        if modules_to_run is None:
            modules_to_run = list(self.modules.keys())
        
        if enable_parallel is None:
            proc_config = self.config_manager.get_processing_config()
            enable_parallel = proc_config.enable_parallel
        
        # Skip channel correlation for single-channel signals
        if (self.metadata['n_channels'] == 1 and 
            'channel_correlation' in modules_to_run):
            self.logger.warning("Skipping channel correlation for single-channel signal")
            modules_to_run.remove('channel_correlation')
        
        # Prepare signal segment
        signal_segment = self._prepare_signal_segment(segment_length)
        
        self.logger.info(f"Running analysis on {len(modules_to_run)} modules "
                        f"({'parallel' if enable_parallel else 'sequential'})")
        
        # Run modules
        if enable_parallel and len(modules_to_run) > 1:
            module_results = self._run_modules_parallel(modules_to_run, signal_segment)
        else:
            module_results = self._run_modules_sequential(modules_to_run, signal_segment)
        
        # Aggregate results
        for result in module_results.values():
            if result is not None:
                self.aggregator.add_module_result(result)
        
        # Generate final summary
        self.results = self.aggregator.generate_summary_report(self.metadata)
        self.processing_time = time.time() - start_time
        
        # Log summary
        overall_score = self.results['overall_assessment']['quality_score']
        overall_grade = self.results['overall_assessment']['quality_grade']
        critical_issues = len(self.results['quality_issues']['critical_issues'])
        
        self.logger.info(f"Analysis complete: Score={overall_score}/100, "
                        f"Grade={overall_grade}, Critical Issues={critical_issues}, "
                        f"Time={self.processing_time:.2f}s")
        
        return self.results
    
    def _run_modules_sequential(self,
                               modules_to_run: List[str],
                               signal_segment: np.ndarray) -> Dict[str, Any]:
        """Run modules sequentially"""
        results = {}
        
        for module_name in modules_to_run:
            if module_name not in self.modules:
                self.logger.warning(f"Module '{module_name}' not found, skipping")
                continue
            
            try:
                self.logger.info(f"Running {module_name}...")
                module = self.modules[module_name]
                
                # Run analysis
                module.analyze(signal_segment, self.metadata)
                result = module.get_results()
                results[module_name] = result
                
                self.logger.info(f"  ✓ {module_name}: Score={result['quality_score']:.1f}, "
                               f"Flags={len(result['flags'])}, "
                               f"Time={result['execution_time']:.3f}s")
                
            except Exception as e:
                self.logger.error(f"  ✗ {module_name} failed: {e}")
                results[module_name] = None
        
        return results
    
    def _run_modules_parallel(self,
                             modules_to_run: List[str],
                             signal_segment: np.ndarray) -> Dict[str, Any]:
        """Run modules in parallel"""
        proc_config = self.config_manager.get_processing_config()
        max_workers = min(proc_config.max_workers, len(modules_to_run))
        
        results = {}
        
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks
            future_to_module = {}
            
            for module_name in modules_to_run:
                if module_name not in self.modules:
                    continue
                
                module = self.modules[module_name]
                future = executor.submit(
                    self._run_single_module,
                    module, signal_segment, self.metadata
                )
                future_to_module[future] = module_name
            
            # Collect results
            for future in as_completed(future_to_module):
                module_name = future_to_module[future]
                
                try:
                    result = future.result()
                    results[module_name] = result
                    
                    if result:
                        self.logger.info(f"  ✓ {module_name}: "
                                       f"Score={result['quality_score']:.1f}, "
                                       f"Flags={len(result['flags'])}")
                    
                except Exception as e:
                    self.logger.error(f"  ✗ {module_name} failed: {e}")
                    results[module_name] = None
        
        return results
    
    @staticmethod
    def _run_single_module(module: BaseQualityModule,
                          signal: np.ndarray,
                          metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Run a single module (for parallel processing)"""
        try:
            module.analyze(signal, metadata)
            return module.get_results()
        except Exception as e:
            return None
    
    def _prepare_signal_segment(self, segment_length: Optional[float]) -> np.ndarray:
        """
        Prepare signal segment for analysis
        
        Parameters:
        - segment_length: Segment length in seconds
        
        Returns:
        - signal_segment: Prepared signal segment
        """
        if segment_length is None:
            proc_config = self.config_manager.get_processing_config()
            segment_length = proc_config.segment_length_sec
        
        sampling_rate = self.metadata['sampling_rate']
        segment_samples = int(segment_length * sampling_rate)
        
        if self.signal.ndim == 1:
            signal_length = len(self.signal)
        else:
            signal_length = self.signal.shape[1]
        
        # Use entire signal if shorter than requested segment
        if signal_length <= segment_samples:
            return self.signal
        
        # Extract middle segment
        start_idx = (signal_length - segment_samples) // 2
        end_idx = start_idx + segment_samples
        
        if self.signal.ndim == 1:
            return self.signal[start_idx:end_idx]
        else:
            return self.signal[:, start_idx:end_idx]
    
    def generate_report(self,
                       output_dir: Optional[str] = None,
                       report_format: str = 'both') -> str:
        """
        Generate comprehensive assessment report
        
        Parameters:
        - output_dir: Output directory (None = use config)
        - report_format: Report format ('pdf', 'json', 'both')
        
        Returns:
        - report_path: Path to generated report
        """
        if not self.results:
            raise ValueError("No assessment results available. Run analysis first.")
        
        if output_dir is None:
            output_config = self.config_manager.get_output_config()
            output_dir = output_config.output_directory
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize report generator
        viz_config = self.config_manager.get_visualization_config()
        report_generator = ReportGenerator(output_dir, viz_config)
        
        # Generate report
        report_path = report_generator.generate_comprehensive_report(
            signal=self.signal,
            metadata=self.metadata,
            results=self.results,
            module_results=self.aggregator.module_results,
            format=report_format
        )
        
        self.logger.info(f"Report generated: {report_path}")
        return report_path
    
    def get_summary(self) -> Dict[str, Any]:
        """
        Get quick summary of assessment results
        
        Returns:
        - summary: Concise results summary
        """
        if not self.results:
            return {"error": "No assessment results available"}
        
        overall = self.results['overall_assessment']
        issues = self.results['quality_issues']
        
        return {
            'signal_info': {
                'channels': self.metadata.get('n_channels', 0),
                'duration_sec': round(self.metadata.get('duration_seconds', 0), 1),
                'sampling_rate': self.metadata.get('sampling_rate', 0),
                'format': self.metadata.get('file_format', 'Unknown')
            },
            'quality_assessment': {
                'overall_score': overall['quality_score'],
                'grade': overall['quality_grade'],
                'assessment_level': overall['assessment_level']
            },
            'issues': {
                'critical_count': len(issues['critical_issues']),
                'total_flags': issues['total_flags'],
                'flag_distribution': issues['flag_distribution']
            },
            'processing': {
                'modules_run': overall['total_modules_run'],
                'processing_time_sec': round(self.processing_time, 2)
            },
            'top_recommendations': self.results['recommendations'].get('immediate', [])[:3]
        }
    
    def get_module_details(self, module_name: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed results for specific module
        
        Parameters:
        - module_name: Name of module
        
        Returns:
        - module_details: Detailed module results or None
        """
        if not self.results:
            return None
        
        detailed_results = self.results.get('detailed_results', {})
        return detailed_results.get(module_name)
    
    def export_results(self,
                      output_file: str,
                      format: str = 'json') -> bool:
        """
        Export results to file
        
        Parameters:
        - output_file: Path to output file
        - format: Export format ('json', 'csv', 'xlsx')
        
        Returns:
        - success: True if export successful
        """
        if not self.results:
            self.logger.error("No results to export")
            return False
        
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if format.lower() == 'json':
                import json
                with open(output_file, 'w') as f:
                    json.dump(self.results, f, indent=2, default=str)
            
            elif format.lower() == 'csv':
                self._export_csv(output_file)
            
            elif format.lower() == 'xlsx':
                self._export_xlsx(output_file)
            
            else:
                raise ValueError(f"Unsupported export format: {format}")
            
            self.logger.info(f"Results exported to {output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export results: {e}")
            return False
    
    def _export_csv(self, output_file: str):
        """Export results to CSV format"""
        import pandas as pd
        
        # Prepare data for CSV export
        rows = []
        
        # Overall summary
        overall = self.results['overall_assessment']
        rows.append({
            'Category': 'Overall',
            'Metric': 'Quality Score',
            'Value': overall['quality_score'],
            'Unit': 'points (0-100)'
        })
        rows.append({
            'Category': 'Overall',
            'Metric': 'Quality Grade',
            'Value': overall['quality_grade'],
            'Unit': 'letter grade'
        })
        
        # Module scores
        module_scores = self.results['module_performance']['scores']
        for module, score in module_scores.items():
            rows.append({
                'Category': 'Module Score',
                'Metric': module,
                'Value': score,
                'Unit': 'points (0-100)'
            })
        
        df = pd.DataFrame(rows)
        df.to_csv(output_file, index=False)
    
    def _export_xlsx(self, output_file: str):
        """Export results to Excel format"""
        import pandas as pd
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # Summary sheet
            summary_data = {
                'Metric': ['Overall Score', 'Grade', 'Critical Issues', 'Total Flags'],
                'Value': [
                    self.results['overall_assessment']['quality_score'],
                    self.results['overall_assessment']['quality_grade'],
                    len(self.results['quality_issues']['critical_issues']),
                    self.results['quality_issues']['total_flags']
                ]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='Summary', index=False)
            
            # Module scores sheet
            module_data = []
            for module, score in self.results['module_performance']['scores'].items():
                module_data.append({'Module': module, 'Score': score})
            
            pd.DataFrame(module_data).to_excel(writer, sheet_name='Module Scores', index=False)
    
    def reset(self):
        """Reset processor state for new analysis"""
        self.signal = None
        self.metadata = {}
        self.results = {}
        self.processing_time = 0.0
        self.aggregator = None
        
        # Reset modules
        for module in self.modules.values():
            module.results = {}
            module.quality_score = None
            module.flags = []
            module.recommendations = []
            module.visualizations = []
        
        self.logger.info("Processor state reset")

---

## 6. CLI INTERFACE

### File: `src/main.py`

### Purpose
Command-line interface for the EEG quality assessment tool.

```python
#!/usr/bin/env python3
"""
EEG Quality Assessment Tool - Command Line Interface

Usage:
    eeg-quality run --signal PATH [OPTIONS]
    eeg-quality batch --input-dir DIR [OPTIONS]
    eeg-quality config --show | --validate PATH
    eeg-quality modules --list | --info MODULE
"""

import click
import sys
from pathlib import Path
import json
import time
from typing import List, Optional

from core.eeg_processor import EEGProcessor
from core.config_manager import ConfigManager


def _display_summary(summary: dict, verbose: bool = False):
    """Display assessment summary in the terminal."""
    click.echo("\n" + "="*50)
    click.echo("EEG QUALITY ASSESSMENT SUMMARY")
    click.echo("="*50)

    # Signal info
    click.echo(f"Signal: {summary['signal_info']['channels']} channels | "
               f"{summary['signal_info']['duration_sec']}s | "
               f"{summary['signal_info']['sampling_rate']} Hz")

    # Quality grade
    score = summary['quality_assessment']['overall_score']
    grade = summary['quality_assessment']['grade']
    level = summary['quality_assessment']['assessment_level']
    click.echo(f"\nOverall Quality: {score}/100 (Grade {grade}) - {level}")

    # Critical issues
    critical = summary['issues']['critical_count']
    total = summary['issues']['total_flags']
    click.echo(f"Issues: {critical} critical, {total} total flags")

    # Recommendations
    recommendations = summary['top_recommendations']
    if recommendations:
        click.echo("\nTop Recommendations:")
        for rec in recommendations:
            click.echo(f"  • {rec}")

    if verbose:
        click.echo(f"\nModules Run: {summary['processing']['modules_run']}")
        click.echo(f"Processing Time: {summary['processing']['processing_time_sec']}s")
        click.echo(f"Flag Distribution: {summary['issues']['flag_distribution']}")


@click.group()
@click.version_option(version='1.0.0')
def cli():
    """EEG Quality Assessment Tool - Comprehensive signal quality analysis"""
    pass


@cli.command()
@click.option('--signal', '-s', required=True, type=click.Path(exists=True),
              help='Path to EEG signal file')
@click.option('--output', '-o', type=click.Path(), default='output',
              help='Output directory for results')
@click.option('--config', '-c', type=click.Path(exists=True),
              help='Custom configuration file')
@click.option('--modules', '-m', multiple=True,
              help='Specific modules to run (default: all)')
@click.option('--format', '-f', type=click.Choice(['pdf', 'json', 'both']),
              default='both', help='Output format')
@click.option('--segment-length', type=float,
              help='Signal segment length in seconds')
@click.option('--parallel/--sequential', default=True,
              help='Enable/disable parallel processing')
@click.option('--verbose', '-v', is_flag=True,
              help='Verbose output')
@click.option('--quick', is_flag=True,
              help='Quick analysis mode (reduced processing)')
def run(signal, output, config, modules, format, segment_length, parallel, verbose, quick):
    """Run quality assessment on a single EEG signal file"""
    
    try:
        # Initialize processor
        if verbose:
            click.echo(f"Initializing EEG Quality Processor...")
        
        processor = EEGProcessor(config_file=config)
        
        # Load signal
        click.echo(f"Loading signal: {signal}")
        if not processor.load_signal_from_file(signal):
            click.echo("Error: Failed to load signal file", err=True)
            sys.exit(1)
        
        # Configure modules for quick mode
        if quick:
            # Use subset of fastest modules
            modules = modules or ['signal_integrity', 'artifact_detection', 'spectral_analysis']
        
        # Run analysis
        click.echo("Running quality assessment...")
        start_time = time.time()
        
        results = processor.run_analysis(
            modules_to_run=list(modules) if modules else None,
            segment_length=segment_length,
            enable_parallel=parallel
        )
        
        processing_time = time.time() - start_time
        
        # Display summary
        summary = processor.get_summary()
        _display_summary(summary, verbose)
        
        # Generate report
        if format in ['pdf', 'both']:
            click.echo("Generating PDF report...")
            processor.generate_report(output, 'pdf')
        
        if format in ['json', 'both']:
            click.echo("Saving JSON results...")
            json_path = Path(output) / 'results.json'
            processor.export_results(str(json_path), 'json')
        
        click.echo(f"\n✅ Analysis complete! Results saved to: {output}")
        click.echo(f"Total processing time: {processing_time:.2f} seconds")
        
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        if verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


@cli.command()
@click.option('--input-dir', '-i', required=True, type=click.Path(exists=True),
              help='Directory containing EEG signal files')
@click.option('--output-dir', '-o', type=click.Path(), default='batch_output',
              help='Output directory for batch results')
@click.option('--config', '-c', type=click.Path(exists=True),
              help='Custom configuration file')
@click.option('--pattern', default='*.edf',
              help='File pattern to match (e.g., "*.edf", "*.fif")')
@click.option('--parallel-files', type=int, default=2,
              help='Number of files to process in parallel')
@click.option('--format', '-f', type=click.Choice(['pdf', 'json', 'both']),
              default='json', help='Output format')
@click.option('--continue-on-error', is_flag=True,
              help='Continue processing other files if one fails')
@click.option('--verbose', '-v', is_flag=True,
              help='Verbose output')
def batch(input_dir, output_dir, config, pattern, parallel_files, format, 
          continue_on_error, verbose):
    """Run quality assessment on multiple EEG signal files"""
    
    try:
        # Find input files
        input_path = Path(input_dir)
        signal_files = list(input_path.glob(pattern))
        
        if not signal_files:
            click.echo(f"No files found matching pattern: {pattern}")
            sys.exit(1)
        
        click.echo(f"Found {len(signal_files)} files to process")
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Process files
        successful = 0
        failed = 0
        batch_results = []
        
        for i, signal_file in enumerate(signal_files, 1):
            click.echo(f"\n[{i}/{len(signal_files)}] Processing: {signal_file.name}")
            
            try:
                # Initialize processor for each file
                processor = EEGProcessor(config_file=config)
                
                # Load and analyze
                if not processor.load_signal_from_file(signal_file):
                    raise ValueError("Failed to load signal")
                
                results = processor.run_analysis()
                
                # Create output subdirectory
                file_output_dir = output_path / signal_file.stem
                file_output_dir.mkdir(exist_ok=True)
                
                # Generate reports
                if format in ['pdf', 'both']:
                    processor.generate_report(str(file_output_dir), 'pdf')
                
                if format in ['json', 'both']:
                    json_path = file_output_dir / 'results.json'
                    processor.export_results(str(json_path), 'json')
                
                # Store batch summary
                summary = processor.get_summary()
                summary['file_path'] = str(signal_file)
                batch_results.append(summary)
                
                successful += 1
                click.echo(f"  ✅ Success - Score: {summary['quality_assessment']['overall_score']}")
                
            except Exception as e:
                failed += 1
                click.echo(f"  ❌ Failed: {e}")
                
                if not continue_on_error:
                    raise
        
        # Save batch summary
        batch_summary = {
            'processed_files': len(signal_files),
            'successful': successful,
            'failed': failed,
            'results': batch_results
        }
        
        batch_summary_path = output_path / 'batch_summary.json'
        with open(batch_summary_path, 'w') as f:
            json.dump(batch_summary, f, indent=2, default=str)
        
        click.echo(f"\n🎉 Batch processing complete!")
        click.echo(f"Successful: {successful}, Failed: {failed}")
        click.echo(f"Results saved to: {output_dir}")
        
    except Exception as e:
        click.echo(f"Batch processing error: {e}", err=True)
        if verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


@cli.command()
@click.option('--show', is_flag=True, help='Show current configuration')
@click.option('--validate', type=click.Path(exists=True), 
              help='Validate configuration file')
@click.option('--export', type=click.Path(),
              help='Export template configuration')
@click.option('--format', type=click.Choice(['json', 'yaml']), default='yaml',
              help='Configuration format')
def config(show, validate, export, format):
    """Configuration management commands"""
    
    try:
        if show:
            # Show current configuration
            config_manager = ConfigManager()
            config_json = json.dumps(config_manager.config, indent=2, default=str)
            click.echo("Current Configuration:")
            click.echo(config_json)
        
        elif validate:
            # Validate configuration file
            click.echo(f"Validating configuration: {validate}")
            try:
                config_manager = ConfigManager(str(validate))
                click.echo("✅ Configuration is valid")
            except Exception as e:
                click.echo(f"❌ Configuration validation failed: {e}", err=True)
                sys.exit(1)
        
        elif export:
            # Export template configuration
            config_manager = ConfigManager()
            extension = '.yaml' if format == 'yaml' else '.json'
            if not str(export).endswith(extension):
                export = str(export) + extension
            
            config_manager.export_template_config(str(export))
            click.echo(f"Template configuration exported to: {export}")
        
        else:
            click.echo("Please specify an action: --show, --validate, or --export")
    
    except Exception as e:
        click.echo(f"Configuration error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--list', 'list_modules', is_flag=True, 
              help='List available modules')
@click.option('--info', help='Show detailed information about a module')
def modules(list_modules, info):
    """Module information and management"""
    
    try:
        processor = EEGProcessor()
        
        if list_modules:
            click.echo("Available Quality Assessment Modules:")
            click.echo("=" * 40)
            
            module_descriptions = {
                'signal_integrity': 'Basic signal validation and integrity checks',
                'artifact_detection': 'Detection and quantification of signal artifacts',
                'spectral_analysis': 'Frequency domain quality assessment',
                'temporal_consistency': 'Time-based signal stability analysis',
                'complexity_entropy': 'Signal complexity and entropy measures',
                'channel_correlation': 'Multi-channel correlation analysis',
                'impedance_contact': 'Electrode contact quality estimation'
            }
            
            for module_name in processor.modules.keys():
                description = module_descriptions.get(module_name, 'No description available')
                click.echo(f"• {module_name}: {description}")
        
        elif info:
            # Show detailed module information
            if info in processor.modules:
                module = processor.modules[info]
                config = processor.config_manager.get_module_config(info)
                
                click.echo(f"Module: {info}")
                click.echo("=" * (len(info) + 8))
                click.echo(f"Class: {module.__class__.__name__}")
                click.echo(f"Configuration parameters: {len(config)}")
                
                if config:
                    click.echo("\nKey Parameters:")
                    for key, value in list(config.items())[:5]:  # Show first 5
                        click.echo(f"  • {key}: {value}")
                    
                    if len(config) > 5:
                        click.echo(f"  ... and {len(config) - 5} more")
            else:
                click.echo(f"Module '{info}' not found")
                click.echo("Use --list to see available modules")
        
        else:
            click.echo("Please specify an action: --list or --info MODULE_NAME")
    
    except Exception as e:
        click.echo(f"Module command error: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()