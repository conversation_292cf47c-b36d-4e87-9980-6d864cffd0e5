```markdown
<!-- docs/modules_1_2_implementation.md -->
# Implementation Guide – Modules 1 & 2  
*(Signal Integrity & Artifact Contamination)*

This document provides **production-ready** skeletons that plug directly into the existing `BaseQualityModule` framework, respect the configuration system, and ship with unit-test stubs.

---

## 1. Quick Reference

| Module | File | Core Dependencies |
|---|---|---|
| 1 – Signal Integrity | `src/modules/signal_integrity.py` | `numpy`, `scipy.stats` |
| 2 – Artifact Contamination | `src/modules/artifact_detection.py` | `numpy`, `scipy.signal`, `scipy.stats` |

---

## 2. Common Base

Both inherit:

```python
from ..core.base_module import BaseQualityModule
```

---

## 3. Module 1 – Signal Integrity Assessor

### 3.1 File: `src/modules/signal_integrity.py`

```python
"""
Module 1: Signal Integrity Assessor
"""
import numpy as np
from typing import Dict, Any, List
from scipy import stats
from ..core.base_module import BaseQualityModule
from ..utils.validation import sanitize_nan

class SignalIntegrityAssessor(BaseQualityModule):
    _module_name = "signal_integrity"

    def __init__(self, sampling_rate: float, config: Dict[str, Any] = None):
        super().__init__(sampling_rate, config)

    # ---------- public API ----------
    def analyze(self, signal: np.ndarray, metadata: Dict[str, Any]) -> Dict[str, Any]:
        signal = sanitize_nan(signal if signal.ndim == 2 else signal[np.newaxis, :])
        results = {}
        for ch_idx, ch in enumerate(signal):
            results[f"ch_{ch_idx}"] = {
                "snr_db": self._calculate_snr(ch),
                "clipping": self._detect_clipping(ch),
                "rms_amplitude": self._calculate_rms(ch),
                "dynamic_range_usage": self._dynamic_range_usage(ch, metadata.get("bit_depth", 16)),
                "zero_crossing_rate": self._zero_crossing_rate(ch),
            }
        self._score = self._calculate_overall_score(results)
        self._flags = self._generate_flags(results)
        self._recommendations = self._generate_recommendations(results)
        return results

    # ---------- helpers ----------
    def _calculate_snr(self, x: np.ndarray) -> float:
        # High-frequency band as noise proxy (45-55 Hz, avoid line noise)
        freqs = np.fft.rfftfreq(len(x), d=1 / self.sampling_rate)
        psd = np.abs(np.fft.rfft(x)) ** 2 / len(x)
        mask_sig = (freqs >= 1) & (freqs <= 40)
        mask_noise = (freqs >= 45) & (freqs <= 55)
        signal_power = psd[mask_sig].mean() if mask_sig.any() else 1e-9
        noise_power = psd[mask_noise].mean() if mask_noise.any() else 1e-9
        return float(10 * np.log10(signal_power / noise_power))

    def _detect_clipping(self, x: np.ndarray) -> Dict[str, Any]:
        clip_min, clip_max = np.percentile(x, [0.01, 99.99])
        clipped = ((x <= clip_min) | (x >= clip_max)).astype(int)
        pct = float(clipped.mean() * 100)
        return {
            "clipping_percent": pct,
            "clipped_samples": int(clipped.sum()),
            "clipping_events": int(np.diff(np.concatenate(([0], clipped, [0]))).clip(0).sum()),
        }

    def _calculate_rms(self, x: np.ndarray) -> Dict[str, float]:
        win = int(self.sampling_rate)  # 1-second windows
        n = len(x) // win
        rms_windows = [float(np.sqrt(np.mean(x[i * win : (i + 1) * win] ** 2))) for i in range(n)]
        return {"overall_rms": float(np.sqrt(np.mean(x ** 2))), "rms_windows": rms_windows}

    def _dynamic_range_usage(self, x: np.ndarray, bit_depth: int) -> float:
        adc_range = 2 ** (bit_depth - 1)
        used = np.ptp(x) / (2 * adc_range)
        return float(np.clip(used * 100, 0, 100))

    def _zero_crossing_rate(self, x: np.ndarray) -> Dict[str, float]:
        zc = np.diff(np.sign(x)).astype(bool).sum()
        rate = float(zc / (len(x) / self.sampling_rate))
        return {"zero_crossing_rate_hz": rate}

    def _calculate_overall_score(self, results: Dict[str, Any]) -> float:
        scores = []
        for ch in results.values():
            sc = 0
            sc += 40 * np.clip(ch["snr_db"] / 20, 0, 1)
            sc += 25 * (1 - np.clip(ch["clipping"]["clipping_percent"] / 5, 0, 1))
            sc += 20 * np.clip(ch["dynamic_range_usage"] / 80, 0, 1)
            sc += 15 * np.clip(ch["zero_crossing_rate_hz"] / 20, 0, 1)
            scores.append(sc)
        return float(np.mean(scores)) if scores else 0.0

    def _generate_flags(self, results: Dict[str, Any]) -> List[str]:
        flags = []
        for ch in results.values():
            if ch["clipping"]["clipping_percent"] > 5:
                flags.append("CLIPPED")
            if ch["snr_db"] < 10:
                flags.append("LOW_SNR")
            if np.std(ch["rms_windows"]) < 0.5:
                flags.append("FLAT_SIGNAL")
        return list(set(flags))

    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        recs = []
        if "CLIPPED" in self._flags:
            recs.append("Reduce gain or check ADC saturation.")
        if "LOW_SNR" in self._flags:
            recs.append("Improve electrode contact or reduce noise sources.")
        return recs
```

---

## 4. Module 2 – Artifact Contamination Detector

### 4.1 File: `src/modules/artifact_detection.py`

```python
"""
Module 2: Artifact Contamination Detector
"""
import numpy as np
from typing import Dict, Any, List
from scipy import signal, stats
from ..core.base_module import BaseQualityModule
from ..utils.validation import sanitize_nan

class ArtifactContaminationDetector(BaseQualityModule):
    _module_name = "artifact_detection"

    def __init__(self, sampling_rate: float, config: Dict[str, Any] = None):
        super().__init__(sampling_rate, config)

    def analyze(self, signal: np.ndarray, metadata: Dict[str, Any]) -> Dict[str, Any]:
        signal = sanitize_nan(signal if signal.ndim == 2 else signal[np.newaxis, :])
        results = {}
        for ch_idx, ch in enumerate(signal):
            results[f"ch_{ch_idx}"] = {
                "muscle": self._detect_muscle_artifacts(ch),
                "drift": self._detect_drift_artifacts(ch),
                "spikes": self._detect_amplitude_spikes(ch),
                "kurtosis": self._analyze_kurtosis(ch),
                "line_noise": self._detect_line_noise(ch),
                "p2p": self._analyze_peak_to_peak(ch),
            }
        self._score = self._calculate_overall_score(results)
        self._flags = self._generate_flags(results)
        self._recommendations = self._generate_recommendations(results)
        return results

    # ---------- helpers ----------
    def _detect_muscle_artifacts(self, x: np.ndarray) -> Dict[str, float]:
        f_min, f_max = self.cfg.get("muscle_freq_range", (30, 100))
        b, a = signal.butter(4, [f_min, f_max], btype="band", fs=self.sampling_rate)
        muscle_signal = signal.filtfilt(b, a, x)
        total_power = np.var(x)
        muscle_power = np.var(muscle_signal)
        ratio = muscle_power / (total_power + 1e-15)
        return {"muscle_power_ratio": float(ratio)}

    def _detect_drift_artifacts(self, x: np.ndarray) -> Dict[str, float]:
        f_min, f_max = self.cfg.get("drift_freq_range", (0.1, 1))
        b, a = signal.butter(4, f_max, btype="low", fs=self.sampling_rate)
        drift = signal.filtfilt(b, a, x)
        ratio = np.var(drift) / (np.var(x) + 1e-15)
        return {"drift_power_ratio": float(ratio)}

    def _detect_amplitude_spikes(self, x: np.ndarray) -> Dict[str, Any]:
        threshold = self.cfg.get("spike_threshold_std", 5.0) * np.std(x)
        spikes = np.abs(x) > threshold
        return {
            "spike_count": int(spikes.sum()),
            "spike_percent": float(spikes.mean() * 100),
        }

    def _analyze_kurtosis(self, x: np.ndarray) -> Dict[str, float]:
        win = int(self.cfg.get("kurtosis_window_sec", 2.0) * self.sampling_rate)
        step = win // 2
        k_vals = [stats.kurtosis(x[i : i + win]) for i in range(0, len(x) - win, step)]
        k_vals = np.array(k_vals)
        return {
            "mean_kurtosis": float(np.mean(k_vals)),
            "max_kurtosis": float(np.max(k_vals)),
        }

    def _detect_line_noise(self, x: np.ndarray) -> Dict[str, Any]:
        line_freq = self.cfg.get("line_noise_freq", 50)
        f, psd = welch(x, self.sampling_rate, nperseg=min(2048, len(x)))
        mask = (f >= line_freq - 1) & (f <= line_freq + 1)
        line_power = psd[mask].sum() if mask.any() else 0
        total_power = psd.sum()
        ratio = float(line_power / (total_power + 1e-15))
        return {"line_noise_ratio": ratio}

    def _analyze_peak_to_peak(self, x: np.ndarray) -> Dict[str, float]:
        win = int(self.sampling_rate)  # 1-second windows
        n = len(x) // win
        p2p = [np.ptp(x[i * win : (i + 1) * win]) for i in range(n)]
        cv = np.std(p2p) / (np.mean(p2p) + 1e-15)
        return {"p2p_cv": float(cv), "max_p2p": float(np.max(p2p))}

    # ---------- scoring ----------
    def _calculate_overall_score(self, results: Dict[str, Any]) -> float:
        weights = {
            "muscle": 25,
            "drift": 20,
            "spikes": 20,
            "line_noise": 15,
            "kurtosis": 10,
            "p2p": 10,
        }
        scores = []
        for ch in results.values():
            sc = 0
            sc += weights["muscle"] * (1 - np.clip(ch["muscle"]["muscle_power_ratio"] / 0.3, 0, 1))
            sc += weights["drift"] * (1 - np.clip(ch["drift"]["drift_power_ratio"] / 0.2, 0, 1))
            sc += weights["spikes"] * (1 - np.clip(ch["spikes"]["spike_percent"] / 1, 0, 1))
            sc += weights["line_noise"] * (1 - np.clip(ch["line_noise"]["line_noise_ratio"] / 0.05, 0, 1))
            kurt = ch["kurtosis"]["max_kurtosis"]
            sc += weights["kurtosis"] * (1 - np.clip((kurt - 3) / 6, 0, 1))
            sc += weights["p2p"] * (1 - np.clip(ch["p2p"]["p2p_cv"] / 1, 0, 1))
            scores.append(sc)
        return float(np.mean(scores)) if scores else 0.0

    def _generate_flags(self, results: Dict[str, Any]) -> List[str]:
        flags = []
        for ch in results.values():
            if ch["muscle"]["muscle_power_ratio"] > 0.3:
                flags.append("MUSCLE_ARTIFACT")
            if ch["drift"]["drift_power_ratio"] > 0.2:
                flags.append("DRIFT_ARTIFACT")
            if ch["line_noise"]["line_noise_ratio"] > 0.05:
                flags.append("LINE_NOISE")
            if ch["spikes"]["spike_percent"] > 1:
                flags.append("AMPLITUDE_SPIKES")
        return list(set(flags))

    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        recs = []
        if "MUSCLE_ARTIFACT" in self._flags:
            recs.append("Ask subject to relax facial/neck muscles.")
        if "DRIFT_ARTIFACT" in self._flags:
            recs.append("Apply high-pass filter (≥0.5 Hz).")
        if "LINE_NOISE" in self._flags:
            recs.append("Enable notch filter at 50/60 Hz.")
        return recs
```

---

## 5. Configuration Snippet (append to `thresholds.yaml`)

```yaml
signal_integrity:
  min_snr_db: 10
  max_clipping_percent: 5
  min_dynamic_range: 20
  flat_signal_threshold: 0.1

artifact_detection:
  muscle_freq_range: [30, 100]
  drift_freq_range: [0.1, 1]
  spike_threshold_std: 5.0
  kurtosis_threshold: 4.0
  line_noise_freq: 50
```

---

## 6. Unit-Test Skeletons

Create `tests/test_modules/test_signal_integrity.py`:

```python
import numpy as np
import pytest
from eeg_quality_assessment.modules.signal_integrity import SignalIntegrityAssessor

def test_snr_clean():
    fs = 1000
    t = np.arange(fs)
    clean = 5 * np.sin(2 * np.pi * 10 * t / fs)
    mod = SignalIntegrityAssessor(fs)
    res = mod.analyze(clean, {})
    assert res["ch_0"]["snr_db"] > 25
```

Repeat similar patterns for **clipping**, **muscle artifacts**, etc.

---

## 7. Final Notes

- Both modules are **channel-agnostic**: accept (n_channels, n_samples) **or** (n_samples,)  
- All functions use **only NumPy/SciPy** for speed and portability.  
- Thresholds are fully configurable via YAML.  
- Output artefacts (PNG/PDF) are handled by the visualization layer – **no plotting here**.

**Status:** Ready for implementation & CI.