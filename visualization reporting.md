# EEG Quality Assessment - Visualization and Reporting System

## Overview
This document specifies the implementation of the visualization and PDF reporting system that aggregates results from all quality assessment modules.

---

## VISUALIZATION COMPONENTS

### File Structure
```
src/visualization/
├── __init__.py
├── signal_plots.py          # Basic signal visualization
├── spectral_plots.py        # Frequency domain plots
├── quality_dashboard.py     # Overall quality dashboard
├── report_generator.py      # PDF report generation
└── plot_utils.py           # Common plotting utilities
```

---

## 1. SIGNAL PLOTS MODULE

### File: `src/visualization/signal_plots.py`

### Purpose
Generate time-domain signal visualizations with quality annotations.

### Key Classes and Functions

#### SignalPlotter Class
```python
class SignalPlotter:
    def __init__(self, sampling_rate: float, channel_names: list = None):
        self.sampling_rate = sampling_rate
        self.channel_names = channel_names or ["EEG"]
        
    def plot_time_series(self, signal: np.ndarray, 
                        title: str = "EEG Signal",
                        annotations: dict = None,
                        time_range: tuple = None) -> plt.Figure:
        """
        Plot EEG signal time series with optional quality annotations
        
        Parameters:
        - signal: EEG data (1D or 2D array)
        - annotations: Dictionary with artifact markers, quality periods, etc.
        - time_range: (start_time, end_time) in seconds for zooming
        
        Returns:
        - matplotlib.Figure: Plot figure
        """
        
    def plot_multichannel_overview(self, multichannel_signal: np.ndarray,
                                  bad_channels: list = None,
                                  quality_scores: dict = None) -> plt.Figure:
        """
        Create stacked plot of all channels with quality indicators
        """
        
    def plot_amplitude_distribution(self, signal: np.ndarray,
                                   channel_idx: int = None) -> plt.Figure:
        """
        Plot amplitude histogram with normality indicators
        """
        
    def plot_quality_timeline(self, signal: np.ndarray,
                             quality_scores: list,
                             window_size: float = 1.0) -> plt.Figure:
        """
        Show how quality varies over time in sliding windows
        """
```

---

## 2. SPECTRAL PLOTS MODULE

### File: `src/visualization/spectral_plots.py`

### Purpose  
Generate frequency-domain visualizations and spectrograms.

### Key Classes and Functions

#### SpectralPlotter Class
```python
class SpectralPlotter:
    def __init__(self, sampling_rate: float):
        self.sampling_rate = sampling_rate
        
    def plot_power_spectral_density(self, signal: np.ndarray,
                                   freq_bands: dict = None,
                                   annotations: dict = None) -> plt.Figure:
        """
        Plot PSD with EEG frequency band annotations and quality indicators
        
        Parameters:
        - signal: EEG signal (1D or 2D)
        - freq_bands: Dictionary of frequency bands to highlight
        - annotations: Line noise markers, artifact bands, etc.
        """
        
    def plot_spectrogram(self, signal: np.ndarray,
                        channel_idx: int = 0,
                        window: str = 'hann',
                        nperseg: int = None) -> plt.Figure:
        """
        Generate spectrogram with time-frequency quality assessment
        """
        
    def plot_1f_slope_analysis(self, signal: np.ndarray,
                              fit_results: dict) -> plt.Figure:
        """
        Plot 1/f slope analysis with neural vs. artifact classification
        """
        
    def plot_coherence_matrix(self, coherence_matrix: np.ndarray,
                             channel_names: list,
                             freq_band: str = "alpha") -> plt.Figure:
        """
        Visualize inter-channel coherence as heatmap
        """
        
    def plot_alpha_peak_detection(self, psd_data: dict,
                                 alpha_results: dict) -> plt.Figure:
        """
        Show alpha peak detection results with quality indicators
        """
```

---

## 3. QUALITY DASHBOARD MODULE

### File: `src/visualization/quality_dashboard.py`

### Purpose
Create comprehensive visual dashboard showing all quality metrics.

### Key Classes and Functions

#### QualityDashboard Class
```python
class QualityDashboard:
    def __init__(self, module_results: dict):
        self.module_results = module_results
        self.overall_score = self._calculate_overall_score()
        
    def create_dashboard(self, signal: np.ndarray = None) -> plt.Figure:
        """
        Create comprehensive quality dashboard
        
        Layout:
        - Top: Overall quality score and grade
        - Middle: Module scores in traffic light format  
        - Bottom: Key signal plots with annotations
        
        Returns:
        - Large matplotlib figure with subplots
        """
        
    def create_traffic_light_display(self) -> plt.Figure:
        """
        Traffic light (red/yellow/green) display for each module
        """
        
    def create_radar_chart(self) -> plt.Figure:
        """
        Radar/spider chart showing scores across all modules
        """
        
    def create_flags_summary(self) -> plt.Figure:
        """
        Visual summary of all quality flags raised
        """
        
    def create_recommendations_panel(self) -> plt.Figure:
        """
        Text-based panel with prioritized recommendations
        """
```

---

## 4. REPORT GENERATOR MODULE

### File: `src/visualization/report_generator.py`

### Purpose
Generate comprehensive PDF reports combining all analyses.

### Key Classes and Functions

#### PDFReportGenerator Class
```python
class PDFReportGenerator:
    def __init__(self, output_dir: str = "reports"):
        self.output_dir = output_dir
        self.report_template = self._load_template()
        
    def generate_full_report(self, 
                           signal: np.ndarray,
                           sampling_rate: float,
                           channel_names: list,
                           module_results: dict,
                           metadata: dict = None) -> str:
        """
        Generate complete PDF report
        
        Returns:
        - report_path: Path to generated PDF file
        """
        
    def _create_executive_summary(self, module_results: dict) -> dict:
        """
        Create executive summary with key findings
        """
        
    def _generate_module_analysis_pages(self, module_results: dict) -> list:
        """
        Create detailed pages for each module's results
        """
        
    def _create_signal_visualizations(self, signal: np.ndarray,
                                    module_results: dict) -> list:
        """
        Generate all signal plots for inclusion in report
        """
        
    def _create_technical_appendix(self, module_results: dict) -> dict:
        """
        Create technical appendix with all metrics and thresholds
        """
```

### PDF Report Structure
```
1. Executive Summary (1-2 pages)
   - Overall quality score and grade (A/B/C/D/F)
   - Key findings and critical issues
   - Quick action items

2. Signal Overview (2-3 pages)
   - Time series plots with annotations
   - Basic signal statistics
   - Channel overview (if multichannel)

3. Module-by-Module Analysis (7-10 pages)
   - One page per module
   - Detailed metrics and scores
   - Module-specific visualizations
   - Individual recommendations

4. Spectral Analysis Summary (2-3 pages)
   - Power spectral density plots
   - Frequency band analysis
   - Coherence analysis (if multichannel)

5. Quality Timeline and Trends (1-2 pages)
   - How quality varies across signal
   - Temporal consistency analysis
   - Problematic time periods highlighted

6. Recommendations and Action Items (1-2 pages)
   - Prioritized list of improvements
   - Specific technical suggestions
   - Equipment/setup recommendations

7. Technical Appendix (2-3 pages)
   - All computed metrics table
   - Thresholds and parameters used
   - Configuration details
   - Processing notes
```

---

## 5. PLOT UTILITIES MODULE

### File: `src/visualization/plot_utils.py`

### Purpose
Common utilities and styling for consistent plot appearance.

### Key Functions

```python
def setup_eeg_plot_style():
    """Set up consistent matplotlib style for EEG plots"""
    
def add_quality_annotations(ax: plt.Axes, 
                          annotations: dict,
                          signal_length: int,
                          sampling_rate: float):
    """Add quality-related annotations to any plot"""
    
def create_frequency_band_shading(ax: plt.Axes, 
                                freq_bands: dict = None):
    """Add frequency band background shading to spectral plots"""
    
def format_time_axis(ax: plt.Axes, 
                    signal_length: int, 
                    sampling_rate: float):
    """Standardize time axis formatting"""
    
def create_quality_colormap():
    """Create custom colormap for quality visualization"""
    
def save_plot_for_report(fig: plt.Figure, 
                        filename: str, 
                        output_dir: str = "temp_plots",
                        dpi: int = 300) -> str:
    """Save plot with consistent formatting for report inclusion"""
```

### Default EEG Frequency Bands
```python
DEFAULT_FREQ_BANDS = {
    'delta': (0.5, 4),
    'theta': (4, 8), 
    'alpha': (8, 13),
    'beta': (13, 30),
    'gamma': (30, 100)
}
```

### Quality Color Scheme
```python
QUALITY_COLORS = {
    'excellent': '#2E7D32',  # Dark green
    'good': '#66BB6A',       # Light green  
    'fair': '#FFA726',       # Orange
    'poor': '#EF5350',       # Red
    'critical': '#B71C1C'    # Dark red
}

QUALITY_THRESHOLDS = {
    'excellent': 90,
    'good': 75,
    'fair': 60,
    'poor': 40,
    'critical': 0
}
```

---

## INTEGRATION WITH MAIN SYSTEM

### Usage in Main Assessment Pipeline
```python
from src.visualization import (
    SignalPlotter, SpectralPlotter, 
    QualityDashboard, PDFReportGenerator
)

# After running all modules
dashboard = QualityDashboard(module_results)
dashboard_fig = dashboard.create_dashboard(signal)

# Generate PDF report
report_gen = PDFReportGenerator("output/reports")
report_path = report_gen.generate_full_report(
    signal=eeg_signal,
    sampling_rate=fs,
    channel_names=channels,
    module_results=all_module_results,
    metadata={"recording_info": "...", "patient_id": "..."}
)
```

### Configuration Options
```python
visualization_config = {
    "plot_style": "publication",  # or "presentation", "web"
    "color_scheme": "colorblind_friendly",
    "dpi": 300,
    "figure_size_inches": (12, 8),
    "font_size": 10,
    "save_individual_plots": True,
    "plot_formats": ["png", "pdf", "svg"],
    "report_template": "standard"  # or "detailed", "brief"
}
```

### Dependencies to Add to Requirements
```
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0  # For interactive plots (optional)
reportlab>=3.6.0  # For PDF generation
jinja2>=3.0.0  # For report templates
pillow>=8.0.0  # For image processing
```

---

## OUTPUT EXAMPLES

### Generated Files
```
output/
├── reports/
│   ├── eeg_quality_report_20250123_143022.pdf
│   └── eeg_quality_summary_20250123_143022.json
├── plots/
│   ├── signal_timeseries.png
│   ├── power_spectral_density.png
│   ├── quality_dashboard.png
│   └── spectrogram.png
└── data/
    ├── processed_signal.npy
    └── quality_metrics.json
```

This visualization system provides comprehensive visual feedback on EEG signal quality, making the assessment results accessible to both technical and clinical users.