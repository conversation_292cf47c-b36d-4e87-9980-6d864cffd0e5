# EEG Quality Assessment - Modules 5, 6, 7 Implementation

## Overview
This document specifies the implementation of the final core assessment modules:
- **Module 5**: Complexity & Entropy Assessor
- **Module 6**: Channel Correlation Analyzer  
- **Module 7**: Impedance & Contact Quality Estimator

---

## MODULE 5: COMPLEXITY & ENTROPY ASSESSOR

### Purpose
Evaluate signal complexity and information content to distinguish between meaningful neural activity and random noise or overly regular artifacts.

### File Structure
```
src/modules/module5_complexity_entropy.py
tests/test_module5_complexity_entropy.py
```

### Class Definition
```python
class ComplexityEntropyAssessor(BaseQualityModule):
    def __init__(self, sampling_rate: float, config: dict = None):
        super().__init__("Complexity & Entropy Assessor", sampling_rate, config)
```

### Key Methods to Implement

#### 1. Sample Entropy Calculation
```python
def calculate_sample_entropy(self, signal: np.ndarray, m: int = 2, r: float = None) -> float:
    """
    Calculate Sample Entropy - measures signal regularity/predictability
    
    Parameters:
    - signal: EEG signal segment
    - m: pattern length (default: 2)
    - r: tolerance (default: 0.2 * std(signal))
    
    Returns:
    - sample_entropy: Higher values indicate more irregular/complex signal
    """
```

#### 2. Approximate Entropy Calculation
```python
def calculate_approximate_entropy(self, signal: np.ndarray, m: int = 2, r: float = None) -> float:
    """
    Calculate Approximate Entropy - complexity measure
    
    Parameters:
    - signal: EEG signal segment
    - m: pattern length
    - r: tolerance for matching
    
    Returns:
    - approximate_entropy: Signal complexity measure
    """
```

#### 3. Lempel-Ziv Complexity
```python
def calculate_lz_complexity(self, signal: np.ndarray, normalize: bool = True) -> float:
    """
    Calculate Lempel-Ziv Complexity - information content measure
    
    Parameters:
    - signal: EEG signal (will be binarized)
    - normalize: Whether to normalize by sequence length
    
    Returns:
    - lz_complexity: Information content measure
    """
```

#### 4. Hjorth Complexity
```python
def calculate_hjorth_complexity(self, signal: np.ndarray) -> float:
    """
    Calculate Hjorth Complexity parameter
    
    Formula: sqrt(Mobility of derivative / Mobility of signal)
    Where Mobility = sqrt(variance(derivative) / variance(signal))
    
    Returns:
    - hjorth_complexity: Signal structural complexity
    """
```

#### 5. Spectral Entropy
```python
def calculate_spectral_entropy(self, signal: np.ndarray, nperseg: int = None) -> float:
    """
    Calculate Spectral Entropy - frequency domain entropy
    
    Parameters:
    - signal: EEG signal
    - nperseg: Length of each segment for PSD calculation
    
    Returns:
    - spectral_entropy: Frequency domain complexity measure
    """
```

### Quality Scoring Logic
```python
def calculate_quality_score(self, metrics: dict) -> int:
    """
    Score based on optimal complexity ranges for EEG:
    - Sample Entropy: 0.5-2.0 (optimal range)
    - Approximate Entropy: 0.4-1.5 (optimal range)
    - LZ Complexity: 0.3-0.8 (normalized, optimal range)
    - Hjorth Complexity: 1.2-2.5 (optimal range)
    - Spectral Entropy: 0.6-0.9 (optimal range)
    
    Score penalties:
    - Values outside optimal ranges reduce score
    - Extremely low/high values indicate artifacts or noise
    """
```

### Flags to Generate
- `TOO_REGULAR`: All entropy measures below lower thresholds
- `TOO_RANDOM`: All entropy measures above upper thresholds  
- `LOW_COMPLEXITY`: Hjorth complexity < 1.0
- `HIGH_ENTROPY`: Spectral entropy > 0.95

---

## MODULE 6: CHANNEL CORRELATION ANALYZER

### Purpose
Assess inter-channel relationships to detect bad channels and validate spatial consistency (multi-channel signals only).

### File Structure
```
src/modules/module6_channel_correlation.py
tests/test_module6_channel_correlation.py
```

### Class Definition
```python
class ChannelCorrelationAnalyzer(BaseQualityModule):
    def __init__(self, sampling_rate: float, channel_names: list, config: dict = None):
        super().__init__("Channel Correlation Analyzer", sampling_rate, config)
        self.channel_names = channel_names
        self.is_multichannel = len(channel_names) > 1
```

### Key Methods to Implement

#### 1. Cross-Channel Correlation Matrix
```python
def calculate_correlation_matrix(self, multichannel_signal: np.ndarray) -> np.ndarray:
    """
    Calculate correlation matrix between all channel pairs
    
    Parameters:
    - multichannel_signal: Shape (n_channels, n_samples)
    
    Returns:
    - correlation_matrix: (n_channels, n_channels) correlation matrix
    """
```

#### 2. Coherence Analysis
```python
def calculate_coherence_matrix(self, multichannel_signal: np.ndarray, 
                             freq_bands: dict = None) -> dict:
    """
    Calculate frequency-specific coherence between channels
    
    Parameters:
    - multichannel_signal: Shape (n_channels, n_samples)
    - freq_bands: {'delta': (0.5, 4), 'theta': (4, 8), ...}
    
    Returns:
    - coherence_results: Dictionary with coherence matrices per frequency band
    """
```

#### 3. Phase Consistency Analysis
```python
def calculate_phase_consistency(self, multichannel_signal: np.ndarray) -> dict:
    """
    Analyze phase relationships between channels
    
    Returns:
    - phase_metrics: Phase lag index, weighted phase lag index, etc.
    """
```

#### 4. Global Field Power
```python
def calculate_global_field_power(self, multichannel_signal: np.ndarray) -> float:
    """
    Calculate Global Field Power - overall spatial activity measure
    
    GFP = std(all_channels_at_each_timepoint).mean()
    
    Returns:
    - gfp: Global field power value
    """
```

#### 5. Bad Channel Detection
```python
def detect_bad_channels(self, multichannel_signal: np.ndarray, 
                       correlation_threshold: float = 0.1) -> list:
    """
    Identify channels that don't correlate well with neighbors
    
    Parameters:
    - correlation_threshold: Minimum correlation with neighboring channels
    
    Returns:
    - bad_channels: List of channel indices/names with poor correlation
    """
```

### Single Channel Handling
```python
def analyze_single_channel(self, signal: np.ndarray) -> dict:
    """
    Handle single-channel case - return neutral scores and skip analysis
    """
```

### Quality Scoring Logic
```python
def calculate_quality_score(self, metrics: dict) -> int:
    """
    Score based on:
    - Number of bad channels detected (penalty)
    - Average inter-channel correlation (should be moderate, not too high/low)
    - Coherence patterns matching expected spatial relationships
    - Phase consistency across channels
    
    Single-channel signals receive neutral score (75)
    """
```

### Flags to Generate
- `BAD_CHANNEL`: Specific channels with poor correlation
- `LOW_COHERENCE`: Overall low inter-channel coherence
- `PHASE_INCONSISTENT`: Inconsistent phase relationships
- `ISOLATED_CHANNEL`: Channel(s) not correlating with any neighbors

---

## MODULE 7: IMPEDANCE & CONTACT QUALITY ESTIMATOR

### Purpose
Estimate electrode-skin interface quality from signal characteristics when direct impedance measurements aren't available.

### File Structure
```
src/modules/module7_impedance_contact.py
tests/test_module7_impedance_contact.py
```

### Class Definition
```python
class ImpedanceContactEstimator(BaseQualityModule):
    def __init__(self, sampling_rate: float, config: dict = None):
        super().__init__("Impedance & Contact Quality Estimator", sampling_rate, config)
```

### Key Methods to Implement

#### 1. Impedance Estimation
```python
def estimate_impedance_from_signal(self, signal: np.ndarray) -> dict:
    """
    Estimate electrode impedance from signal characteristics:
    - High-frequency noise level (higher impedance = more noise)
    - Signal amplitude variability
    - Baseline stability
    
    Returns:
    - impedance_metrics: Estimated impedance category and confidence
    """
```

#### 2. Contact Quality Assessment
```python
def assess_contact_quality(self, signal: np.ndarray) -> dict:
    """
    Evaluate electrode-scalp coupling quality:
    - Signal stability over time
    - Absence of sudden jumps/pops
    - Appropriate signal amplitude range
    
    Returns:
    - contact_metrics: Contact quality indicators
    """
```

#### 3. Baseline Drift Analysis
```python
def analyze_baseline_drift(self, signal: np.ndarray, window_size: int = None) -> dict:
    """
    Assess baseline wander indicating poor contact:
    - Low-frequency trend analysis
    - Drift rate calculation
    - Stability assessment
    
    Returns:
    - drift_metrics: Baseline stability measures
    """
```

#### 4. Pop/Disconnection Detection
```python
def detect_electrode_pops(self, signal: np.ndarray, threshold_factor: float = 5.0) -> dict:
    """
    Detect sudden impedance changes (electrode pops):
    - Sudden amplitude jumps
    - High-frequency transients
    - Step changes in baseline
    
    Returns:
    - pop_metrics: Number and timing of detected pops
    """
```

#### 5. Connection Stability Index
```python
def calculate_stability_index(self, signal: np.ndarray, segment_length: int = None) -> float:
    """
    Calculate overall connection stability:
    - Consistency of signal characteristics over time
    - Absence of intermittent connectivity issues
    
    Returns:
    - stability_index: 0-1 stability measure
    """
```

### Quality Scoring Logic
```python
def calculate_quality_score(self, metrics: dict) -> int:
    """
    Score based on:
    - Estimated impedance level (lower is better)
    - Contact stability (higher is better)
    - Absence of pops/disconnections
    - Minimal baseline drift
    - Overall connection reliability
    
    High impedance, pops, or drift significantly reduce score
    """
```

### Flags to Generate
- `HIGH_IMPEDANCE`: Estimated impedance above acceptable threshold
- `POOR_CONTACT`: Multiple indicators of bad electrode contact
- `UNSTABLE_CONNECTION`: Variable connection quality over time
- `ELECTRODE_POP`: Detected sudden impedance changes

---

## Integration Requirements

### Base Class Compatibility
All modules must inherit from `BaseQualityModule` and implement:
- `analyze(signal)`: Main analysis method
- `get_results()`: Return standardized results dictionary
- `generate_recommendations()`: Provide actionable suggestions

### Configuration Support
Each module should support configuration parameters:
```python
default_config = {
    "complexity_entropy": {
        "sample_entropy_m": 2,
        "sample_entropy_r_factor": 0.2,
        "lz_normalize": True,
        "optimal_ranges": {
            "sample_entropy": [0.5, 2.0],
            "approximate_entropy": [0.4, 1.5],
            "lz_complexity": [0.3, 0.8]
        }
    },
    "channel_correlation": {
        "correlation_threshold": 0.1,
        "coherence_freq_bands": {
            "delta": [0.5, 4],
            "theta": [4, 8], 
            "alpha": [8, 13],
            "beta": [13, 30]
        }
    },
    "impedance_contact": {
        "impedance_threshold": "medium",
        "pop_threshold_factor": 5.0,
        "drift_window_sec": 2.0
    }
}
```

### Error Handling
- Handle single-channel vs multi-channel gracefully
- Provide meaningful warnings for edge cases
- Return appropriate default values for failed calculations

### Performance Considerations
- Optimize entropy calculations for long signals
- Use efficient correlation/coherence algorithms
- Consider downsampling for very high sampling rates where appropriate